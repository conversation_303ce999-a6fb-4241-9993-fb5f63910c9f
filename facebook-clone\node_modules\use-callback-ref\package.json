{"name": "use-callback-ref", "version": "1.3.3", "description": "The same useRef, but with callback", "main": "dist/es5/index.js", "jsnext:main": "dist/es2015/index.js", "module": "dist/es2015/index.js", "types": "dist/es5/index.d.ts", "module:es2019": "dist/es2019/index.js", "sideEffects": false, "scripts": {"dev": "lib-builder dev", "test": "jest", "test:ci": "jest --runInBand --coverage", "build": "lib-builder build && yarn size:report", "release": "yarn build && yarn test", "size": "npx size-limit", "size:report": "npx size-limit --json > .size.json", "lint": "lib-builder lint", "format": "lib-builder format", "update": "lib-builder update", "prepublish": "yarn build && yarn changelog", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "changelog:rewrite": "conventional-changelog -p angular -i CHANGELOG.md -s -r 0"}, "repository": "https://github.com/theKashey/use-callback-ref/", "author": "theK<PERSON>ey <<EMAIL>>", "license": "MIT", "dependencies": {"tslib": "^2.0.0"}, "devDependencies": {"@size-limit/preset-small-lib": "^11.0.2", "size-limit": "^11.0.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@theuiteam/lib-builder": "^0.3.0", "jest-environment-jsdom": "^29.7.0"}, "peerDependencies": {"@types/react": "*", "react": "^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc"}, "peerDependenciesMeta": {"@types/react": {"optional": true}}, "engines": {"node": ">=10"}, "files": ["dist"], "keywords": ["react", "hook", "useRef", "createRef", "merge refs"], "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{ts,tsx}": ["prettier --write", "eslint --fix", "git add"], "*.{js,css,json,md}": ["prettier --write", "git add"]}, "prettier": {"printWidth": 120, "trailingComma": "es5", "tabWidth": 2, "semi": true, "singleQuote": true}}