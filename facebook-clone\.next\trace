[{"name": "hot-reloader", "duration": 178, "timestamp": 294617554544, "id": 3, "tags": {"version": "15.1.8"}, "startTime": 1748097608428, "traceId": "00c4c28060a41f95"}, {"name": "setup-dev-bundler", "duration": 1201166, "timestamp": 294617200419, "id": 2, "parentId": 1, "tags": {}, "startTime": 1748097608073, "traceId": "00c4c28060a41f95"}, {"name": "run-instrumentation-hook", "duration": 67, "timestamp": 294618526727, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748097609400, "traceId": "00c4c28060a41f95"}, {"name": "start-dev-server", "duration": 2497656, "timestamp": 294616076694, "id": 1, "tags": {"cpus": "8", "platform": "win32", "memory.freeMem": "1090338816", "memory.totalMem": "8250900480", "memory.heapSizeLimit": "4175429632", "memory.rss": "165027840", "memory.heapTotal": "99155968", "memory.heapUsed": "59964488"}, "startTime": 1748097606950, "traceId": "00c4c28060a41f95"}, {"name": "compile-path", "duration": 8067067, "timestamp": 294661601342, "id": 7, "tags": {"trigger": "/"}, "startTime": 1748097652474, "traceId": "00c4c28060a41f95"}, {"name": "ensure-page", "duration": 8071798, "timestamp": 294661599329, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1748097652472, "traceId": "00c4c28060a41f95"}]