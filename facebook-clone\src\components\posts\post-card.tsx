'use client'

import { useState } from 'react'
import { formatDistanceToNow } from 'date-fns'
import { Heart, MessageCircle, Share, MoreHorizontal } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useAuthStore } from '@/lib/stores/auth-store'
import { usePostsStore } from '@/lib/stores/posts-store'

interface Post {
  id: string
  user_id: string
  content: string
  image_url: string | null
  likes_count: number
  comments_count: number
  created_at: string
  updated_at: string
  profiles: {
    full_name: string | null
    avatar_url: string | null
  }
  user_has_liked?: boolean
}

interface PostCardProps {
  post: Post
}

export function PostCard({ post }: PostCardProps) {
  const { user } = useAuthStore()
  const { likePost, unlikePost, addComment, fetchComments, comments } = usePostsStore()
  const [showComments, setShowComments] = useState(false)
  const [newComment, setNewComment] = useState('')
  const [isCommenting, setIsCommenting] = useState(false)

  const handleLike = async () => {
    if (post.user_has_liked) {
      await unlikePost(post.id)
    } else {
      await likePost(post.id)
    }
  }

  const handleComment = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newComment.trim()) return

    setIsCommenting(true)
    try {
      const { error } = await addComment(post.id, newComment.trim())
      if (!error) {
        setNewComment('')
      }
    } catch (err) {
      console.error('Error adding comment:', err)
    } finally {
      setIsCommenting(false)
    }
  }

  const handleShowComments = async () => {
    if (!showComments) {
      await fetchComments(post.id)
    }
    setShowComments(!showComments)
  }

  const postComments = comments[post.id] || []

  return (
    <Card className="mb-4">
      <CardContent className="p-4">
        {/* Post Header */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <Avatar className="w-10 h-10">
              <AvatarImage src={post.profiles.avatar_url || ''} />
              <AvatarFallback>
                {post.profiles.full_name?.charAt(0) || 'U'}
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className="font-semibold text-sm">
                {post.profiles.full_name || 'Unknown User'}
              </h3>
              <p className="text-xs text-gray-500">
                {formatDistanceToNow(new Date(post.created_at), { addSuffix: true })}
              </p>
            </div>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="w-4 h-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem>Save post</DropdownMenuItem>
              <DropdownMenuItem>Hide post</DropdownMenuItem>
              {user?.id === post.user_id && (
                <>
                  <DropdownMenuItem>Edit post</DropdownMenuItem>
                  <DropdownMenuItem className="text-red-600">Delete post</DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Post Content */}
        <div className="mb-4">
          <p className="text-gray-900 whitespace-pre-wrap">{post.content}</p>
          {post.image_url && (
            <div className="mt-3">
              <img
                src={post.image_url}
                alt="Post image"
                className="w-full rounded-lg max-h-96 object-cover"
              />
            </div>
          )}
        </div>

        {/* Post Stats */}
        {(post.likes_count > 0 || post.comments_count > 0) && (
          <div className="flex items-center justify-between py-2 border-b border-gray-200 mb-3">
            <div className="flex items-center space-x-2">
              {post.likes_count > 0 && (
                <div className="flex items-center space-x-1">
                  <div className="w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center">
                    <Heart className="w-3 h-3 text-white fill-current" />
                  </div>
                  <span className="text-sm text-gray-600">{post.likes_count}</span>
                </div>
              )}
            </div>
            {post.comments_count > 0 && (
              <button
                onClick={handleShowComments}
                className="text-sm text-gray-600 hover:underline"
              >
                {post.comments_count} comment{post.comments_count !== 1 ? 's' : ''}
              </button>
            )}
          </div>
        )}

        {/* Post Actions */}
        <div className="flex items-center justify-between mb-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleLike}
            className={`flex-1 ${post.user_has_liked ? 'text-blue-600' : 'text-gray-600'}`}
          >
            <Heart className={`w-5 h-5 mr-2 ${post.user_has_liked ? 'fill-current' : ''}`} />
            Like
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleShowComments}
            className="flex-1 text-gray-600"
          >
            <MessageCircle className="w-5 h-5 mr-2" />
            Comment
          </Button>
          <Button variant="ghost" size="sm" className="flex-1 text-gray-600">
            <Share className="w-5 h-5 mr-2" />
            Share
          </Button>
        </div>

        {/* Comments Section */}
        {showComments && (
          <div className="space-y-3">
            {/* Existing Comments */}
            {postComments.map((comment) => (
              <div key={comment.id} className="flex space-x-2">
                <Avatar className="w-8 h-8">
                  <AvatarImage src={comment.profiles.avatar_url || ''} />
                  <AvatarFallback>
                    {comment.profiles.full_name?.charAt(0) || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <div className="bg-gray-100 rounded-lg px-3 py-2">
                    <p className="font-semibold text-sm">{comment.profiles.full_name}</p>
                    <p className="text-sm">{comment.content}</p>
                  </div>
                  <p className="text-xs text-gray-500 mt-1 ml-3">
                    {formatDistanceToNow(new Date(comment.created_at), { addSuffix: true })}
                  </p>
                </div>
              </div>
            ))}

            {/* Add Comment */}
            <form onSubmit={handleComment} className="flex space-x-2">
              <Avatar className="w-8 h-8">
                <AvatarImage src={user?.user_metadata?.avatar_url || ''} />
                <AvatarFallback>
                  {user?.email?.charAt(0) || 'U'}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 flex space-x-2">
                <Textarea
                  placeholder="Write a comment..."
                  value={newComment}
                  onChange={(e) => setNewComment(e.target.value)}
                  className="flex-1 min-h-[40px] resize-none"
                  rows={1}
                />
                <Button
                  type="submit"
                  size="sm"
                  disabled={!newComment.trim() || isCommenting}
                >
                  {isCommenting ? 'Posting...' : 'Post'}
                </Button>
              </div>
            </form>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
