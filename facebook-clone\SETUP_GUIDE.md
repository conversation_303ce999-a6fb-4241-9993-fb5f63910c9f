# 🚀 Facebook Clone - Complete Setup Guide

## 📋 **Quick Setup Checklist**

- [ ] Create Supabase account and project
- [ ] Run database schema
- [ ] Configure authentication settings
- [ ] Update environment variables
- [ ] Test the connection
- [ ] Start using the app!

---

## 🔥 **Step-by-Step Setup**

### **1. Create Supabase Project (5 minutes)**

1. **Go to [supabase.com](https://supabase.com)**
2. **Sign up/Login** with GitHub, Google, or email
3. **Click "New Project"**
4. **Fill in project details:**
   ```
   Name: facebook-clone
   Database Password: [Create a strong password - SAVE IT!]
   Region: [Choose closest to your location]
   ```
5. **Click "Create new project"**
6. **Wait 2-3 minutes** for project initialization

### **2. Set Up Database Schema (2 minutes)**

1. **In your Supabase dashboard, go to "SQL Editor"**
2. **Click "New query"**
3. **Copy the entire content from `supabase-schema.sql`**
4. **Paste it into the SQL Editor**
5. **Click "Run"**
6. **You should see "Success. No rows returned"**

### **3. Configure Authentication (1 minute)**

1. **Go to "Authentication" → "Settings"**
2. **Under "Auth Providers", ensure "Email" is enabled**
3. **Set "Site URL" to:** `http://localhost:3000`
4. **Add "Redirect URLs":**
   - `http://localhost:3000`
   - `http://localhost:3000/auth/callback`
5. **Turn OFF "Enable email confirmations"** (for development)

### **4. Get Your Credentials (1 minute)**

1. **Go to "Settings" → "API"**
2. **Copy your Project URL** (looks like: `https://abcdefgh.supabase.co`)
3. **Copy your anon/public key** (long string starting with `eyJhbGciOiJIUzI1NiIs...`)

### **5. Update Environment Variables (1 minute)**

1. **Open `.env.local` in your project**
2. **Replace the placeholder values:**
   ```env
   NEXT_PUBLIC_SUPABASE_URL=https://your-actual-project-id.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_actual_anon_key_here
   ```
3. **Save the file**

### **6. Test Your Setup (1 minute)**

Run the test script to verify everything is working:

```bash
cd facebook-clone
node scripts/test-supabase.js
```

You should see:
```
✅ Environment variables look good!
✅ Database connection successful!
✅ Authentication service is working!
✅ All tables exist and accessible
🎉 Supabase setup is complete and working!
```

### **7. Restart and Test (1 minute)**

1. **Stop the dev server** (Ctrl+C)
2. **Restart:** `npm run dev`
3. **Open:** [http://localhost:3000](http://localhost:3000)

---

## 🧪 **Testing Your Facebook Clone**

### **Test 1: User Registration**
1. Go to [http://localhost:3000/register](http://localhost:3000/register)
2. Create account with:
   - Full Name: `Test User`
   - Email: `<EMAIL>`
   - Password: `password123`
3. You should be automatically logged in and redirected to the news feed

### **Test 2: Create Your First Post**
1. In the "What's on your mind?" box, type:
   ```
   Hello Facebook Clone! 🎉 This is my first post on this amazing platform built with Next.js and Supabase!
   ```
2. Click "Post"
3. Your post should appear immediately in the feed

### **Test 3: Social Interactions**
1. Click the ❤️ "Like" button on your post
2. Click "Comment" and add: `Great first post! 👍`
3. You should see the like count increase and your comment appear

### **Test 4: Authentication Flow**
1. Click your profile picture → "Log Out"
2. You should be redirected to the login page
3. Log back in with your credentials
4. You should see your posts and data preserved

---

## 🎯 **What You'll Have After Setup**

### **🔐 Authentication System**
- ✅ Secure login/register forms
- ✅ Session management
- ✅ Protected routes
- ✅ User profiles

### **📱 Social Features**
- ✅ News feed with real-time updates
- ✅ Post creation with rich text
- ✅ Like and comment system
- ✅ User interactions
- ✅ Responsive design

### **⚡ Advanced Features**
- ✅ Real-time synchronization
- ✅ Optimistic UI updates
- ✅ Error handling
- ✅ Loading states
- ✅ Mobile responsive

---

## 🔧 **Troubleshooting**

### **Problem: Infinite loading spinner**
**Solution:**
- Check `.env.local` has correct Supabase credentials
- Run `node scripts/test-supabase.js` to verify connection
- Check browser console for errors

### **Problem: "Invalid URL" error**
**Solution:**
- Ensure NEXT_PUBLIC_SUPABASE_URL is in format: `https://project-id.supabase.co`
- No trailing slashes or extra characters

### **Problem: Authentication not working**
**Solution:**
- Verify email confirmations are disabled in Supabase
- Check redirect URLs are set correctly
- Ensure anon key is copied completely

### **Problem: Posts not appearing**
**Solution:**
- Verify database schema was executed successfully
- Check RLS policies are enabled
- Run the test script to verify table access

---

## 🎉 **Success Indicators**

You'll know everything is working when you see:

1. **✅ Login page loads without errors**
2. **✅ You can register a new account**
3. **✅ News feed appears after login**
4. **✅ You can create posts**
5. **✅ Like/comment features work**
6. **✅ Real-time updates happen instantly**

---

## 🚀 **Next Steps**

Once your Facebook clone is working:

1. **Invite friends** to test with multiple users
2. **Customize the design** to match your preferences
3. **Add new features** like image uploads or messaging
4. **Deploy to production** using Vercel
5. **Scale the database** as your user base grows

---

**🎯 Your Facebook clone is now ready to use! Enjoy building the next generation of social media! 🚀**
