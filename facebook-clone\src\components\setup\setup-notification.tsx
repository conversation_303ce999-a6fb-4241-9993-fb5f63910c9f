'use client'

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Check } from 'lucide-react'
import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { isSupabaseConfigured } from '@/lib/supabase'

export function SetupNotification() {
  const [copiedUrl, setCopiedUrl] = useState(false)
  const [copiedKey, setCopiedKey] = useState(false)

  if (isSupabaseConfigured()) {
    return null
  }

  const copyToClipboard = async (text: string, type: 'url' | 'key') => {
    try {
      await navigator.clipboard.writeText(text)
      if (type === 'url') {
        setCopiedUrl(true)
        setTimeout(() => setCopiedUrl(false), 2000)
      } else {
        setCopiedKey(true)
        setTimeout(() => setCopiedKey(false), 2000)
      }
    } catch (err) {
      console.error('Failed to copy text: ', err)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="max-w-2xl w-full">
        <CardHeader className="text-center">
          <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-white font-bold text-2xl">f</span>
          </div>
          <CardTitle className="text-2xl">Facebook Clone Setup Required</CardTitle>
          <CardDescription>
            Your Facebook clone is ready! Just complete the 5-minute Supabase setup to unlock all features.
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 flex items-start space-x-3">
            <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
            <div>
              <h3 className="font-medium text-yellow-800">Supabase Configuration Missing</h3>
              <p className="text-sm text-yellow-700 mt-1">
                The application needs Supabase credentials to enable authentication and database features.
              </p>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="font-semibold text-lg">Quick Setup Steps:</h3>
            
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <Badge variant="outline" className="mt-1">1</Badge>
                <div className="flex-1">
                  <p className="font-medium">Create Supabase Project</p>
                  <p className="text-sm text-gray-600 mb-2">
                    Sign up at Supabase and create a new project named "facebook-clone"
                  </p>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => window.open('https://supabase.com', '_blank')}
                  >
                    <ExternalLink className="w-4 h-4 mr-2" />
                    Open Supabase
                  </Button>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Badge variant="outline" className="mt-1">2</Badge>
                <div className="flex-1">
                  <p className="font-medium">Run Database Schema</p>
                  <p className="text-sm text-gray-600">
                    Copy and run the SQL from <code className="bg-gray-100 px-1 rounded">supabase-schema.sql</code> in your Supabase SQL Editor
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Badge variant="outline" className="mt-1">3</Badge>
                <div className="flex-1">
                  <p className="font-medium">Get Your Credentials</p>
                  <p className="text-sm text-gray-600 mb-2">
                    Go to Settings → API in your Supabase dashboard and copy:
                  </p>
                  <div className="space-y-2">
                    <div className="bg-gray-50 p-3 rounded border">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-mono">NEXT_PUBLIC_SUPABASE_URL</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard('NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co', 'url')}
                        >
                          {copiedUrl ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                        </Button>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Your Project URL</p>
                    </div>
                    <div className="bg-gray-50 p-3 rounded border">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-mono">NEXT_PUBLIC_SUPABASE_ANON_KEY</span>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard('NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here', 'key')}
                        >
                          {copiedKey ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
                        </Button>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">Your Anon/Public Key</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Badge variant="outline" className="mt-1">4</Badge>
                <div className="flex-1">
                  <p className="font-medium">Update Environment Variables</p>
                  <p className="text-sm text-gray-600">
                    Add your credentials to <code className="bg-gray-100 px-1 rounded">.env.local</code> and restart the server
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-800 mb-2">What you'll get after setup:</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>✅ User registration and authentication</li>
              <li>✅ Real-time news feed with posts</li>
              <li>✅ Like and comment functionality</li>
              <li>✅ User profiles and social features</li>
              <li>✅ Mobile-responsive design</li>
            </ul>
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-600">
              Need help? Check the <code className="bg-gray-100 px-1 rounded">SETUP_GUIDE.md</code> file for detailed instructions.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
