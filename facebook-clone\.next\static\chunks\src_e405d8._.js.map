{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/fbaug/facebook-clone/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB"}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/fbaug/facebook-clone/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;AAEA;AAHA;;;;;;AAKA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,8JAAM,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG"}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/fbaug/facebook-clone/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,8JAAM,UAAU,MAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG"}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/fbaug/facebook-clone/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAEA;AAGA;AAFA;AAHA;;;;;AAOA,MAAM,uBAAS,8JAAM,UAAU,MAG7B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,IAAI;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;AAGb,OAAO,WAAW,GAAG,sKAAgB,IAAI,CAAC,WAAW;AAErD,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,KAAK;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,sKAAgB,KAAK,CAAC,WAAW;AAE3D,MAAM,+BAAiB,8JAAM,UAAU,OAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,sKAAgB,QAAQ;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,sKAAgB,QAAQ,CAAC,WAAW"}}, {"offset": {"line": 187, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 193, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/fbaug/facebook-clone/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md\",\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-4 [&>svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AAIA;AAHA;AACA;AAAA;AAAA;AAJA;;;;;;AAQA,MAAM,eAAe,gLAAsB,IAAI;AAE/C,MAAM,sBAAsB,gLAAsB,OAAO;AAEzD,MAAM,oBAAoB,gLAAsB,KAAK;AAErD,MAAM,qBAAqB,gLAAsB,MAAM;AAEvD,MAAM,kBAAkB,gLAAsB,GAAG;AAEjD,MAAM,yBAAyB,gLAAsB,UAAU;AAE/D,MAAM,uCAAyB,8JAAM,UAAU,MAK7C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,6LAAC,gLAAsB,UAAU;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0MACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,yNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,gLAAsB,UAAU,CAAC,WAAW;AAE9C,MAAM,uCAAyB,8JAAM,UAAU,OAG7C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,gLAAsB,UAAU;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,gLAAsB,UAAU,CAAC,WAAW;AAE9C,MAAM,oCAAsB,8JAAM,UAAU,OAG1C,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,gLAAsB,MAAM;kBAC3B,cAAA,6LAAC,gLAAsB,OAAO;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sLACA,4YACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,gLAAsB,OAAO,CAAC,WAAW;AAE3E,MAAM,iCAAmB,8JAAM,UAAU,OAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,gLAAsB,IAAI;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yQACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,gLAAsB,IAAI,CAAC,WAAW;AAErE,MAAM,yCAA2B,8JAAM,UAAU,OAG/C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,6LAAC,gLAAsB,YAAY;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,gLAAsB,aAAa;8BAClC,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;AAGL,yBAAyB,WAAW,GAClC,gLAAsB,YAAY,CAAC,WAAW;AAEhD,MAAM,sCAAwB,8JAAM,UAAU,QAG5C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,gLAAsB,SAAS;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,gLAAsB,aAAa;8BAClC,cAAA,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,gLAAsB,SAAS,CAAC,WAAW;AAE/E,MAAM,kCAAoB,8JAAM,UAAU,QAKxC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,gLAAsB,KAAK;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,gLAAsB,KAAK,CAAC,WAAW;AAEvE,MAAM,sCAAwB,8JAAM,UAAU,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,gLAAsB,SAAS;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,sBAAsB,WAAW,GAAG,gLAAsB,SAAS,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG"}}, {"offset": {"line": 415, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/fbaug/facebook-clone/src/components/layout/navbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { useRouter } from 'next/navigation'\nimport { Search, Home, Users, MessageCircle, Bell, Menu } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { useAuthStore } from '@/lib/stores/auth-store'\n\nexport function Navbar() {\n  const router = useRouter()\n  const { user, profile, signOut } = useAuthStore()\n  const [searchQuery, setSearchQuery] = useState('')\n\n  const handleSignOut = async () => {\n    await signOut()\n    router.push('/login')\n  }\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault()\n    if (searchQuery.trim()) {\n      router.push(`/search?q=${encodeURIComponent(searchQuery)}`)\n    }\n  }\n\n  if (!user) return null\n\n  return (\n    <nav className=\"sticky top-0 z-50 bg-white border-b border-gray-200 shadow-sm\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo and Search */}\n          <div className=\"flex items-center space-x-4\">\n            <Link href=\"/\" className=\"flex items-center\">\n              <div className=\"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\">\n                <span className=\"text-white font-bold text-xl\">f</span>\n              </div>\n            </Link>\n            \n            <form onSubmit={handleSearch} className=\"hidden md:block\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n                <Input\n                  type=\"text\"\n                  placeholder=\"Search Facebook\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"pl-10 w-64 bg-gray-100 border-none focus:bg-white\"\n                />\n              </div>\n            </form>\n          </div>\n\n          {/* Navigation Icons */}\n          <div className=\"hidden md:flex items-center space-x-2\">\n            <Link href=\"/\">\n              <Button variant=\"ghost\" size=\"lg\" className=\"p-3\">\n                <Home className=\"w-6 h-6\" />\n              </Button>\n            </Link>\n            <Link href=\"/friends\">\n              <Button variant=\"ghost\" size=\"lg\" className=\"p-3\">\n                <Users className=\"w-6 h-6\" />\n              </Button>\n            </Link>\n            <Link href=\"/messages\">\n              <Button variant=\"ghost\" size=\"lg\" className=\"p-3\">\n                <MessageCircle className=\"w-6 h-6\" />\n              </Button>\n            </Link>\n            <Button variant=\"ghost\" size=\"lg\" className=\"p-3\">\n              <Bell className=\"w-6 h-6\" />\n            </Button>\n          </div>\n\n          {/* User Menu */}\n          <div className=\"flex items-center space-x-3\">\n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button variant=\"ghost\" className=\"p-1\">\n                  <Avatar className=\"w-8 h-8\">\n                    <AvatarImage src={profile?.avatar_url || ''} />\n                    <AvatarFallback>\n                      {profile?.full_name?.charAt(0) || user.email?.charAt(0) || 'U'}\n                    </AvatarFallback>\n                  </Avatar>\n                </Button>\n              </DropdownMenuTrigger>\n              <DropdownMenuContent align=\"end\" className=\"w-56\">\n                <DropdownMenuItem asChild>\n                  <Link href={`/profile/${user.id}`} className=\"flex items-center\">\n                    <Avatar className=\"w-8 h-8 mr-2\">\n                      <AvatarImage src={profile?.avatar_url || ''} />\n                      <AvatarFallback>\n                        {profile?.full_name?.charAt(0) || user.email?.charAt(0) || 'U'}\n                      </AvatarFallback>\n                    </Avatar>\n                    <div>\n                      <div className=\"font-medium\">{profile?.full_name || 'User'}</div>\n                      <div className=\"text-sm text-gray-500\">See your profile</div>\n                    </div>\n                  </Link>\n                </DropdownMenuItem>\n                <DropdownMenuSeparator />\n                <DropdownMenuItem asChild>\n                  <Link href=\"/settings\">Settings & Privacy</Link>\n                </DropdownMenuItem>\n                <DropdownMenuItem asChild>\n                  <Link href=\"/help\">Help & Support</Link>\n                </DropdownMenuItem>\n                <DropdownMenuSeparator />\n                <DropdownMenuItem onClick={handleSignOut}>\n                  Log Out\n                </DropdownMenuItem>\n              </DropdownMenuContent>\n            </DropdownMenu>\n\n            {/* Mobile menu button */}\n            <Button variant=\"ghost\" size=\"sm\" className=\"md:hidden\">\n              <Menu className=\"w-5 h-5\" />\n            </Button>\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AAOA;AAXA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;;;;;;AAkBO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAC9C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,gBAAgB;QACpB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,OAAO,IAAI,CAAC,CAAC,UAAU,EAAE,mBAAmB,cAAc;QAC5D;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;0CACvB,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;;;;;;0CAInD,6LAAC;gCAAK,UAAU;gCAAc,WAAU;0CACtC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC,oIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAOlB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;8CAC1C,cAAA,6LAAC,sMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGpB,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;8CAC1C,cAAA,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGrB,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAK,WAAU;8CAC1C,cAAA,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAG7B,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,WAAU;0CAC1C,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKpB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+IAAA,CAAA,eAAY;;kDACX,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,WAAU;sDAChC,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,6LAAC,qIAAA,CAAA,cAAW;wDAAC,KAAK,SAAS,cAAc;;;;;;kEACzC,6LAAC,qIAAA,CAAA,iBAAc;kEACZ,SAAS,WAAW,OAAO,MAAM,KAAK,KAAK,EAAE,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;kDAKnE,6LAAC,+IAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAM,WAAU;;0DACzC,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,OAAO;0DACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAM,CAAC,SAAS,EAAE,KAAK,EAAE,EAAE;oDAAE,WAAU;;sEAC3C,6LAAC,qIAAA,CAAA,SAAM;4DAAC,WAAU;;8EAChB,6LAAC,qIAAA,CAAA,cAAW;oEAAC,KAAK,SAAS,cAAc;;;;;;8EACzC,6LAAC,qIAAA,CAAA,iBAAc;8EACZ,SAAS,WAAW,OAAO,MAAM,KAAK,KAAK,EAAE,OAAO,MAAM;;;;;;;;;;;;sEAG/D,6LAAC;;8EACC,6LAAC;oEAAI,WAAU;8EAAe,SAAS,aAAa;;;;;;8EACpD,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;0DAI7C,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0DACtB,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,OAAO;0DACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAY;;;;;;;;;;;0DAEzB,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,OAAO;0DACvB,cAAA,6LAAC,+JAAA,CAAA,UAAI;oDAAC,MAAK;8DAAQ;;;;;;;;;;;0DAErB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;0DACtB,6LAAC,+IAAA,CAAA,mBAAgB;gDAAC,SAAS;0DAAe;;;;;;;;;;;;;;;;;;0CAO9C,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,WAAU;0CAC1C,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9B;GAtHgB;;QACC,qIAAA,CAAA,YAAS;QACW,wIAAA,CAAA,eAAY;;;KAFjC"}}, {"offset": {"line": 857, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 863, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/fbaug/facebook-clone/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Textarea = React.forwardRef<\n  HTMLTextAreaElement,\n  React.ComponentProps<\"textarea\">\n>(({ className, ...props }, ref) => {\n  return (\n    <textarea\n      className={cn(\n        \"flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      ref={ref}\n      {...props}\n    />\n  )\n})\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,8JAAM,UAAU,MAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6QACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AACA,SAAS,WAAW,GAAG"}}, {"offset": {"line": 892, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 898, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/fbaug/facebook-clone/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,8JAAM,UAAU,MAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,8JAAM,UAAU,OAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,8JAAM,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,8JAAM,UAAU,OAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,8JAAM,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,8JAAM,UAAU,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG"}}, {"offset": {"line": 995, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1001, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/fbaug/facebook-clone/src/lib/stores/posts-store.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { supabase } from '@/lib/supabase'\n\ninterface Post {\n  id: string\n  user_id: string\n  content: string\n  image_url: string | null\n  likes_count: number\n  comments_count: number\n  created_at: string\n  updated_at: string\n  profiles: {\n    full_name: string | null\n    avatar_url: string | null\n  }\n  user_has_liked?: boolean\n}\n\ninterface Comment {\n  id: string\n  user_id: string\n  post_id: string\n  content: string\n  created_at: string\n  updated_at: string\n  profiles: {\n    full_name: string | null\n    avatar_url: string | null\n  }\n}\n\ninterface PostsState {\n  posts: Post[]\n  loading: boolean\n  comments: { [postId: string]: Comment[] }\n  fetchPosts: () => Promise<void>\n  createPost: (content: string, imageUrl?: string) => Promise<{ error: any }>\n  likePost: (postId: string) => Promise<{ error: any }>\n  unlikePost: (postId: string) => Promise<{ error: any }>\n  addComment: (postId: string, content: string) => Promise<{ error: any }>\n  fetchComments: (postId: string) => Promise<void>\n}\n\nexport const usePostsStore = create<PostsState>((set, get) => ({\n  posts: [],\n  loading: false,\n  comments: {},\n\n  fetchPosts: async () => {\n    set({ loading: true })\n    try {\n      const { data: posts, error } = await supabase\n        .from('posts')\n        .select(`\n          *,\n          profiles:user_id (\n            full_name,\n            avatar_url\n          )\n        `)\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n\n      // Check which posts the current user has liked\n      const { data: { user } } = await supabase.auth.getUser()\n      if (user && posts) {\n        const { data: likes } = await supabase\n          .from('likes')\n          .select('post_id')\n          .eq('user_id', user.id)\n\n        const likedPostIds = new Set(likes?.map(like => like.post_id) || [])\n        \n        const postsWithLikes = posts.map(post => ({\n          ...post,\n          user_has_liked: likedPostIds.has(post.id)\n        }))\n\n        set({ posts: postsWithLikes })\n      } else {\n        set({ posts: posts || [] })\n      }\n    } catch (error) {\n      console.error('Error fetching posts:', error)\n    } finally {\n      set({ loading: false })\n    }\n  },\n\n  createPost: async (content: string, imageUrl?: string) => {\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) return { error: 'Not authenticated' }\n\n      const { data, error } = await supabase\n        .from('posts')\n        .insert({\n          user_id: user.id,\n          content,\n          image_url: imageUrl || null,\n        })\n        .select(`\n          *,\n          profiles:user_id (\n            full_name,\n            avatar_url\n          )\n        `)\n        .single()\n\n      if (error) return { error }\n\n      // Add the new post to the beginning of the posts array\n      const { posts } = get()\n      set({ posts: [{ ...data, user_has_liked: false }, ...posts] })\n\n      return { error: null }\n    } catch (error) {\n      return { error }\n    }\n  },\n\n  likePost: async (postId: string) => {\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) return { error: 'Not authenticated' }\n\n      // Add like\n      const { error: likeError } = await supabase\n        .from('likes')\n        .insert({\n          user_id: user.id,\n          post_id: postId,\n        })\n\n      if (likeError) return { error: likeError }\n\n      // Update likes count\n      const { error: updateError } = await supabase.rpc('increment_likes', {\n        post_id: postId\n      })\n\n      if (updateError) return { error: updateError }\n\n      // Update local state\n      const { posts } = get()\n      const updatedPosts = posts.map(post =>\n        post.id === postId\n          ? { ...post, likes_count: post.likes_count + 1, user_has_liked: true }\n          : post\n      )\n      set({ posts: updatedPosts })\n\n      return { error: null }\n    } catch (error) {\n      return { error }\n    }\n  },\n\n  unlikePost: async (postId: string) => {\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) return { error: 'Not authenticated' }\n\n      // Remove like\n      const { error: unlikeError } = await supabase\n        .from('likes')\n        .delete()\n        .eq('user_id', user.id)\n        .eq('post_id', postId)\n\n      if (unlikeError) return { error: unlikeError }\n\n      // Update likes count\n      const { error: updateError } = await supabase.rpc('decrement_likes', {\n        post_id: postId\n      })\n\n      if (updateError) return { error: updateError }\n\n      // Update local state\n      const { posts } = get()\n      const updatedPosts = posts.map(post =>\n        post.id === postId\n          ? { ...post, likes_count: Math.max(0, post.likes_count - 1), user_has_liked: false }\n          : post\n      )\n      set({ posts: updatedPosts })\n\n      return { error: null }\n    } catch (error) {\n      return { error }\n    }\n  },\n\n  addComment: async (postId: string, content: string) => {\n    try {\n      const { data: { user } } = await supabase.auth.getUser()\n      if (!user) return { error: 'Not authenticated' }\n\n      const { data, error } = await supabase\n        .from('comments')\n        .insert({\n          user_id: user.id,\n          post_id: postId,\n          content,\n        })\n        .select(`\n          *,\n          profiles:user_id (\n            full_name,\n            avatar_url\n          )\n        `)\n        .single()\n\n      if (error) return { error }\n\n      // Update comments count\n      await supabase.rpc('increment_comments', { post_id: postId })\n\n      // Update local state\n      const { posts, comments } = get()\n      const updatedPosts = posts.map(post =>\n        post.id === postId\n          ? { ...post, comments_count: post.comments_count + 1 }\n          : post\n      )\n      const updatedComments = {\n        ...comments,\n        [postId]: [...(comments[postId] || []), data]\n      }\n      set({ posts: updatedPosts, comments: updatedComments })\n\n      return { error: null }\n    } catch (error) {\n      return { error }\n    }\n  },\n\n  fetchComments: async (postId: string) => {\n    try {\n      const { data, error } = await supabase\n        .from('comments')\n        .select(`\n          *,\n          profiles:user_id (\n            full_name,\n            avatar_url\n          )\n        `)\n        .eq('post_id', postId)\n        .order('created_at', { ascending: true })\n\n      if (error) throw error\n\n      const { comments } = get()\n      set({\n        comments: {\n          ...comments,\n          [postId]: data || []\n        }\n      })\n    } catch (error) {\n      console.error('Error fetching comments:', error)\n    }\n  },\n}))\n"], "names": [], "mappings": ";;;AACA;AADA;;;AA4CO,MAAM,gBAAgB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAc,CAAC,KAAK,MAAQ,CAAC;QAC7D,OAAO,EAAE;QACT,SAAS;QACT,UAAU,CAAC;QAEX,YAAY;YACV,IAAI;gBAAE,SAAS;YAAK;YACpB,IAAI;gBACF,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC1C,IAAI,CAAC,SACL,MAAM,CAAC,CAAC;;;;;;QAMT,CAAC,EACA,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAM;gBAE1C,IAAI,OAAO,MAAM;gBAEjB,+CAA+C;gBAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;gBACtD,IAAI,QAAQ,OAAO;oBACjB,MAAM,EAAE,MAAM,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,WACP,EAAE,CAAC,WAAW,KAAK,EAAE;oBAExB,MAAM,eAAe,IAAI,IAAI,OAAO,IAAI,CAAA,OAAQ,KAAK,OAAO,KAAK,EAAE;oBAEnE,MAAM,iBAAiB,MAAM,GAAG,CAAC,CAAA,OAAQ,CAAC;4BACxC,GAAG,IAAI;4BACP,gBAAgB,aAAa,GAAG,CAAC,KAAK,EAAE;wBAC1C,CAAC;oBAED,IAAI;wBAAE,OAAO;oBAAe;gBAC9B,OAAO;oBACL,IAAI;wBAAE,OAAO,SAAS,EAAE;oBAAC;gBAC3B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;YACzC,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;QACF;QAEA,YAAY,OAAO,SAAiB;YAClC,IAAI;gBACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;gBACtD,IAAI,CAAC,MAAM,OAAO;oBAAE,OAAO;gBAAoB;gBAE/C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC;oBACN,SAAS,KAAK,EAAE;oBAChB;oBACA,WAAW,YAAY;gBACzB,GACC,MAAM,CAAC,CAAC;;;;;;QAMT,CAAC,EACA,MAAM;gBAET,IAAI,OAAO,OAAO;oBAAE;gBAAM;gBAE1B,uDAAuD;gBACvD,MAAM,EAAE,KAAK,EAAE,GAAG;gBAClB,IAAI;oBAAE,OAAO;wBAAC;4BAAE,GAAG,IAAI;4BAAE,gBAAgB;wBAAM;2BAAM;qBAAM;gBAAC;gBAE5D,OAAO;oBAAE,OAAO;gBAAK;YACvB,EAAE,OAAO,OAAO;gBACd,OAAO;oBAAE;gBAAM;YACjB;QACF;QAEA,UAAU,OAAO;YACf,IAAI;gBACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;gBACtD,IAAI,CAAC,MAAM,OAAO;oBAAE,OAAO;gBAAoB;gBAE/C,WAAW;gBACX,MAAM,EAAE,OAAO,SAAS,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACxC,IAAI,CAAC,SACL,MAAM,CAAC;oBACN,SAAS,KAAK,EAAE;oBAChB,SAAS;gBACX;gBAEF,IAAI,WAAW,OAAO;oBAAE,OAAO;gBAAU;gBAEzC,qBAAqB;gBACrB,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,mBAAmB;oBACnE,SAAS;gBACX;gBAEA,IAAI,aAAa,OAAO;oBAAE,OAAO;gBAAY;gBAE7C,qBAAqB;gBACrB,MAAM,EAAE,KAAK,EAAE,GAAG;gBAClB,MAAM,eAAe,MAAM,GAAG,CAAC,CAAA,OAC7B,KAAK,EAAE,KAAK,SACR;wBAAE,GAAG,IAAI;wBAAE,aAAa,KAAK,WAAW,GAAG;wBAAG,gBAAgB;oBAAK,IACnE;gBAEN,IAAI;oBAAE,OAAO;gBAAa;gBAE1B,OAAO;oBAAE,OAAO;gBAAK;YACvB,EAAE,OAAO,OAAO;gBACd,OAAO;oBAAE;gBAAM;YACjB;QACF;QAEA,YAAY,OAAO;YACjB,IAAI;gBACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;gBACtD,IAAI,CAAC,MAAM,OAAO;oBAAE,OAAO;gBAAoB;gBAE/C,cAAc;gBACd,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAC1C,IAAI,CAAC,SACL,MAAM,GACN,EAAE,CAAC,WAAW,KAAK,EAAE,EACrB,EAAE,CAAC,WAAW;gBAEjB,IAAI,aAAa,OAAO;oBAAE,OAAO;gBAAY;gBAE7C,qBAAqB;gBACrB,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,mBAAmB;oBACnE,SAAS;gBACX;gBAEA,IAAI,aAAa,OAAO;oBAAE,OAAO;gBAAY;gBAE7C,qBAAqB;gBACrB,MAAM,EAAE,KAAK,EAAE,GAAG;gBAClB,MAAM,eAAe,MAAM,GAAG,CAAC,CAAA,OAC7B,KAAK,EAAE,KAAK,SACR;wBAAE,GAAG,IAAI;wBAAE,aAAa,KAAK,GAAG,CAAC,GAAG,KAAK,WAAW,GAAG;wBAAI,gBAAgB;oBAAM,IACjF;gBAEN,IAAI;oBAAE,OAAO;gBAAa;gBAE1B,OAAO;oBAAE,OAAO;gBAAK;YACvB,EAAE,OAAO,OAAO;gBACd,OAAO;oBAAE;gBAAM;YACjB;QACF;QAEA,YAAY,OAAO,QAAgB;YACjC,IAAI;gBACF,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;gBACtD,IAAI,CAAC,MAAM,OAAO;oBAAE,OAAO;gBAAoB;gBAE/C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;oBACN,SAAS,KAAK,EAAE;oBAChB,SAAS;oBACT;gBACF,GACC,MAAM,CAAC,CAAC;;;;;;QAMT,CAAC,EACA,MAAM;gBAET,IAAI,OAAO,OAAO;oBAAE;gBAAM;gBAE1B,wBAAwB;gBACxB,MAAM,yHAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,sBAAsB;oBAAE,SAAS;gBAAO;gBAE3D,qBAAqB;gBACrB,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG;gBAC5B,MAAM,eAAe,MAAM,GAAG,CAAC,CAAA,OAC7B,KAAK,EAAE,KAAK,SACR;wBAAE,GAAG,IAAI;wBAAE,gBAAgB,KAAK,cAAc,GAAG;oBAAE,IACnD;gBAEN,MAAM,kBAAkB;oBACtB,GAAG,QAAQ;oBACX,CAAC,OAAO,EAAE;2BAAK,QAAQ,CAAC,OAAO,IAAI,EAAE;wBAAG;qBAAK;gBAC/C;gBACA,IAAI;oBAAE,OAAO;oBAAc,UAAU;gBAAgB;gBAErD,OAAO;oBAAE,OAAO;gBAAK;YACvB,EAAE,OAAO,OAAO;gBACd,OAAO;oBAAE;gBAAM;YACjB;QACF;QAEA,eAAe,OAAO;YACpB,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC,CAAC;;;;;;QAMT,CAAC,EACA,EAAE,CAAC,WAAW,QACd,KAAK,CAAC,cAAc;oBAAE,WAAW;gBAAK;gBAEzC,IAAI,OAAO,MAAM;gBAEjB,MAAM,EAAE,QAAQ,EAAE,GAAG;gBACrB,IAAI;oBACF,UAAU;wBACR,GAAG,QAAQ;wBACX,CAAC,OAAO,EAAE,QAAQ,EAAE;oBACtB;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;YAC5C;QACF;IACF,CAAC"}}, {"offset": {"line": 1246, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1252, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/fbaug/facebook-clone/src/components/posts/post-card.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { formatDistanceToNow } from 'date-fns'\nimport { Heart, MessageCircle, Share, MoreHorizontal } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { useAuthStore } from '@/lib/stores/auth-store'\nimport { usePostsStore } from '@/lib/stores/posts-store'\n\ninterface Post {\n  id: string\n  user_id: string\n  content: string\n  image_url: string | null\n  likes_count: number\n  comments_count: number\n  created_at: string\n  updated_at: string\n  profiles: {\n    full_name: string | null\n    avatar_url: string | null\n  }\n  user_has_liked?: boolean\n}\n\ninterface PostCardProps {\n  post: Post\n}\n\nexport function PostCard({ post }: PostCardProps) {\n  const { user } = useAuthStore()\n  const { likePost, unlikePost, addComment, fetchComments, comments } = usePostsStore()\n  const [showComments, setShowComments] = useState(false)\n  const [newComment, setNewComment] = useState('')\n  const [isCommenting, setIsCommenting] = useState(false)\n\n  const handleLike = async () => {\n    if (post.user_has_liked) {\n      await unlikePost(post.id)\n    } else {\n      await likePost(post.id)\n    }\n  }\n\n  const handleComment = async (e: React.FormEvent) => {\n    e.preventDefault()\n    if (!newComment.trim()) return\n\n    setIsCommenting(true)\n    try {\n      const { error } = await addComment(post.id, newComment.trim())\n      if (!error) {\n        setNewComment('')\n      }\n    } catch (err) {\n      console.error('Error adding comment:', err)\n    } finally {\n      setIsCommenting(false)\n    }\n  }\n\n  const handleShowComments = async () => {\n    if (!showComments) {\n      await fetchComments(post.id)\n    }\n    setShowComments(!showComments)\n  }\n\n  const postComments = comments[post.id] || []\n\n  return (\n    <Card className=\"mb-4\">\n      <CardContent className=\"p-4\">\n        {/* Post Header */}\n        <div className=\"flex items-center justify-between mb-4\">\n          <div className=\"flex items-center space-x-3\">\n            <Avatar className=\"w-10 h-10\">\n              <AvatarImage src={post.profiles.avatar_url || ''} />\n              <AvatarFallback>\n                {post.profiles.full_name?.charAt(0) || 'U'}\n              </AvatarFallback>\n            </Avatar>\n            <div>\n              <h3 className=\"font-semibold text-sm\">\n                {post.profiles.full_name || 'Unknown User'}\n              </h3>\n              <p className=\"text-xs text-gray-500\">\n                {formatDistanceToNow(new Date(post.created_at), { addSuffix: true })}\n              </p>\n            </div>\n          </div>\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button variant=\"ghost\" size=\"sm\">\n                <MoreHorizontal className=\"w-4 h-4\" />\n              </Button>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent align=\"end\">\n              <DropdownMenuItem>Save post</DropdownMenuItem>\n              <DropdownMenuItem>Hide post</DropdownMenuItem>\n              {user?.id === post.user_id && (\n                <>\n                  <DropdownMenuItem>Edit post</DropdownMenuItem>\n                  <DropdownMenuItem className=\"text-red-600\">Delete post</DropdownMenuItem>\n                </>\n              )}\n            </DropdownMenuContent>\n          </DropdownMenu>\n        </div>\n\n        {/* Post Content */}\n        <div className=\"mb-4\">\n          <p className=\"text-gray-900 whitespace-pre-wrap\">{post.content}</p>\n          {post.image_url && (\n            <div className=\"mt-3\">\n              <img\n                src={post.image_url}\n                alt=\"Post image\"\n                className=\"w-full rounded-lg max-h-96 object-cover\"\n              />\n            </div>\n          )}\n        </div>\n\n        {/* Post Stats */}\n        {(post.likes_count > 0 || post.comments_count > 0) && (\n          <div className=\"flex items-center justify-between py-2 border-b border-gray-200 mb-3\">\n            <div className=\"flex items-center space-x-2\">\n              {post.likes_count > 0 && (\n                <div className=\"flex items-center space-x-1\">\n                  <div className=\"w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center\">\n                    <Heart className=\"w-3 h-3 text-white fill-current\" />\n                  </div>\n                  <span className=\"text-sm text-gray-600\">{post.likes_count}</span>\n                </div>\n              )}\n            </div>\n            {post.comments_count > 0 && (\n              <button\n                onClick={handleShowComments}\n                className=\"text-sm text-gray-600 hover:underline\"\n              >\n                {post.comments_count} comment{post.comments_count !== 1 ? 's' : ''}\n              </button>\n            )}\n          </div>\n        )}\n\n        {/* Post Actions */}\n        <div className=\"flex items-center justify-between mb-3\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={handleLike}\n            className={`flex-1 ${post.user_has_liked ? 'text-blue-600' : 'text-gray-600'}`}\n          >\n            <Heart className={`w-5 h-5 mr-2 ${post.user_has_liked ? 'fill-current' : ''}`} />\n            Like\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={handleShowComments}\n            className=\"flex-1 text-gray-600\"\n          >\n            <MessageCircle className=\"w-5 h-5 mr-2\" />\n            Comment\n          </Button>\n          <Button variant=\"ghost\" size=\"sm\" className=\"flex-1 text-gray-600\">\n            <Share className=\"w-5 h-5 mr-2\" />\n            Share\n          </Button>\n        </div>\n\n        {/* Comments Section */}\n        {showComments && (\n          <div className=\"space-y-3\">\n            {/* Existing Comments */}\n            {postComments.map((comment) => (\n              <div key={comment.id} className=\"flex space-x-2\">\n                <Avatar className=\"w-8 h-8\">\n                  <AvatarImage src={comment.profiles.avatar_url || ''} />\n                  <AvatarFallback>\n                    {comment.profiles.full_name?.charAt(0) || 'U'}\n                  </AvatarFallback>\n                </Avatar>\n                <div className=\"flex-1\">\n                  <div className=\"bg-gray-100 rounded-lg px-3 py-2\">\n                    <p className=\"font-semibold text-sm\">{comment.profiles.full_name}</p>\n                    <p className=\"text-sm\">{comment.content}</p>\n                  </div>\n                  <p className=\"text-xs text-gray-500 mt-1 ml-3\">\n                    {formatDistanceToNow(new Date(comment.created_at), { addSuffix: true })}\n                  </p>\n                </div>\n              </div>\n            ))}\n\n            {/* Add Comment */}\n            <form onSubmit={handleComment} className=\"flex space-x-2\">\n              <Avatar className=\"w-8 h-8\">\n                <AvatarImage src={user?.user_metadata?.avatar_url || ''} />\n                <AvatarFallback>\n                  {user?.email?.charAt(0) || 'U'}\n                </AvatarFallback>\n              </Avatar>\n              <div className=\"flex-1 flex space-x-2\">\n                <Textarea\n                  placeholder=\"Write a comment...\"\n                  value={newComment}\n                  onChange={(e) => setNewComment(e.target.value)}\n                  className=\"flex-1 min-h-[40px] resize-none\"\n                  rows={1}\n                />\n                <Button\n                  type=\"submit\"\n                  size=\"sm\"\n                  disabled={!newComment.trim() || isCommenting}\n                >\n                  {isCommenting ? 'Posting...' : 'Post'}\n                </Button>\n              </div>\n            </form>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AACA;AACA;AACA;AACA;AAMA;AACA;AAbA;AACA;AAAA;AAAA;AAAA;;;AAJA;;;;;;;;;;;AAsCO,SAAS,SAAS,EAAE,IAAI,EAAiB;;IAC9C,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;IAClF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,aAAa;QACjB,IAAI,KAAK,cAAc,EAAE;YACvB,MAAM,WAAW,KAAK,EAAE;QAC1B,OAAO;YACL,MAAM,SAAS,KAAK,EAAE;QACxB;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,EAAE,cAAc;QAChB,IAAI,CAAC,WAAW,IAAI,IAAI;QAExB,gBAAgB;QAChB,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,WAAW,KAAK,EAAE,EAAE,WAAW,IAAI;YAC3D,IAAI,CAAC,OAAO;gBACV,cAAc;YAChB;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,yBAAyB;QACzC,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,cAAc;YACjB,MAAM,cAAc,KAAK,EAAE;QAC7B;QACA,gBAAgB,CAAC;IACnB;IAEA,MAAM,eAAe,QAAQ,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE;IAE5C,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;;8BAErB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,6LAAC,qIAAA,CAAA,cAAW;4CAAC,KAAK,KAAK,QAAQ,CAAC,UAAU,IAAI;;;;;;sDAC9C,6LAAC,qIAAA,CAAA,iBAAc;sDACZ,KAAK,QAAQ,CAAC,SAAS,EAAE,OAAO,MAAM;;;;;;;;;;;;8CAG3C,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDACX,KAAK,QAAQ,CAAC,SAAS,IAAI;;;;;;sDAE9B,6LAAC;4CAAE,WAAU;sDACV,CAAA,GAAA,qJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,KAAK,UAAU,GAAG;gDAAE,WAAW;4CAAK;;;;;;;;;;;;;;;;;;sCAIxE,6LAAC,+IAAA,CAAA,eAAY;;8CACX,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;kDAC3B,cAAA,6LAAC,mNAAA,CAAA,iBAAc;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAG9B,6LAAC,+IAAA,CAAA,sBAAmB;oCAAC,OAAM;;sDACzB,6LAAC,+IAAA,CAAA,mBAAgB;sDAAC;;;;;;sDAClB,6LAAC,+IAAA,CAAA,mBAAgB;sDAAC;;;;;;wCACjB,MAAM,OAAO,KAAK,OAAO,kBACxB;;8DACE,6LAAC,+IAAA,CAAA,mBAAgB;8DAAC;;;;;;8DAClB,6LAAC,+IAAA,CAAA,mBAAgB;oDAAC,WAAU;8DAAe;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQrD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAqC,KAAK,OAAO;;;;;;wBAC7D,KAAK,SAAS,kBACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,KAAK,KAAK,SAAS;gCACnB,KAAI;gCACJ,WAAU;;;;;;;;;;;;;;;;;gBAOjB,CAAC,KAAK,WAAW,GAAG,KAAK,KAAK,cAAc,GAAG,CAAC,mBAC/C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,KAAK,WAAW,GAAG,mBAClB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,6LAAC;wCAAK,WAAU;kDAAyB,KAAK,WAAW;;;;;;;;;;;;;;;;;wBAI9D,KAAK,cAAc,GAAG,mBACrB,6LAAC;4BACC,SAAS;4BACT,WAAU;;gCAET,KAAK,cAAc;gCAAC;gCAAS,KAAK,cAAc,KAAK,IAAI,MAAM;;;;;;;;;;;;;8BAOxE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAW,CAAC,OAAO,EAAE,KAAK,cAAc,GAAG,kBAAkB,iBAAiB;;8CAE9E,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAW,CAAC,aAAa,EAAE,KAAK,cAAc,GAAG,iBAAiB,IAAI;;;;;;gCAAI;;;;;;;sCAGnF,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAG5C,6LAAC,qIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,WAAU;;8CAC1C,6LAAC,uMAAA,CAAA,QAAK;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;gBAMrC,8BACC,6LAAC;oBAAI,WAAU;;wBAEZ,aAAa,GAAG,CAAC,CAAC,wBACjB,6LAAC;gCAAqB,WAAU;;kDAC9B,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,6LAAC,qIAAA,CAAA,cAAW;gDAAC,KAAK,QAAQ,QAAQ,CAAC,UAAU,IAAI;;;;;;0DACjD,6LAAC,qIAAA,CAAA,iBAAc;0DACZ,QAAQ,QAAQ,CAAC,SAAS,EAAE,OAAO,MAAM;;;;;;;;;;;;kDAG9C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyB,QAAQ,QAAQ,CAAC,SAAS;;;;;;kEAChE,6LAAC;wDAAE,WAAU;kEAAW,QAAQ,OAAO;;;;;;;;;;;;0DAEzC,6LAAC;gDAAE,WAAU;0DACV,CAAA,GAAA,qJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,KAAK,QAAQ,UAAU,GAAG;oDAAE,WAAW;gDAAK;;;;;;;;;;;;;+BAbjE,QAAQ,EAAE;;;;;sCAoBtB,6LAAC;4BAAK,UAAU;4BAAe,WAAU;;8CACvC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,6LAAC,qIAAA,CAAA,cAAW;4CAAC,KAAK,MAAM,eAAe,cAAc;;;;;;sDACrD,6LAAC,qIAAA,CAAA,iBAAc;sDACZ,MAAM,OAAO,OAAO,MAAM;;;;;;;;;;;;8CAG/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,uIAAA,CAAA,WAAQ;4CACP,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC7C,WAAU;4CACV,MAAM;;;;;;sDAER,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,MAAK;4CACL,UAAU,CAAC,WAAW,IAAI,MAAM;sDAE/B,eAAe,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjD;GAvMgB;;QACG,wIAAA,CAAA,eAAY;QACyC,yIAAA,CAAA,gBAAa;;;KAFrE"}}, {"offset": {"line": 1795, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1801, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/fbaug/facebook-clone/src/components/posts/create-post.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Image, Smile, MapPin } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'\nimport { useAuthStore } from '@/lib/stores/auth-store'\nimport { usePostsStore } from '@/lib/stores/posts-store'\n\nexport function CreatePost() {\n  const { user, profile } = useAuthStore()\n  const { createPost } = usePostsStore()\n  const [content, setContent] = useState('')\n  const [isLoading, setIsLoading] = useState(false)\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    if (!content.trim()) return\n\n    setIsLoading(true)\n    try {\n      const { error } = await createPost(content.trim())\n      if (!error) {\n        setContent('')\n      }\n    } catch (err) {\n      console.error('Error creating post:', err)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  if (!user) return null\n\n  return (\n    <Card className=\"mb-6\">\n      <CardContent className=\"p-4\">\n        <form onSubmit={handleSubmit}>\n          <div className=\"flex space-x-3\">\n            <Avatar className=\"w-10 h-10\">\n              <AvatarImage src={profile?.avatar_url || ''} />\n              <AvatarFallback>\n                {profile?.full_name?.charAt(0) || user.email?.charAt(0) || 'U'}\n              </AvatarFallback>\n            </Avatar>\n            <div className=\"flex-1\">\n              <Textarea\n                placeholder={`What's on your mind, ${profile?.full_name?.split(' ')[0] || 'there'}?`}\n                value={content}\n                onChange={(e) => setContent(e.target.value)}\n                className=\"min-h-[100px] resize-none border-none bg-gray-50 focus:bg-white\"\n              />\n            </div>\n          </div>\n\n          <div className=\"mt-4 pt-4 border-t border-gray-200\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"flex space-x-4\">\n                <Button\n                  type=\"button\"\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"text-gray-600 hover:bg-gray-100\"\n                >\n                  <Image className=\"w-5 h-5 mr-2 text-green-600\" />\n                  Photo/Video\n                </Button>\n                <Button\n                  type=\"button\"\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"text-gray-600 hover:bg-gray-100\"\n                >\n                  <Smile className=\"w-5 h-5 mr-2 text-yellow-600\" />\n                  Feeling/Activity\n                </Button>\n                <Button\n                  type=\"button\"\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"text-gray-600 hover:bg-gray-100\"\n                >\n                  <MapPin className=\"w-5 h-5 mr-2 text-red-600\" />\n                  Check In\n                </Button>\n              </div>\n              <Button\n                type=\"submit\"\n                disabled={!content.trim() || isLoading}\n                className=\"bg-blue-600 hover:bg-blue-700\"\n              >\n                {isLoading ? 'Posting...' : 'Post'}\n              </Button>\n            </div>\n          </div>\n        </form>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AANA;AAAA;AAAA;;;AAHA;;;;;;;;;AAWO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IACrC,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,IAAI,CAAC,QAAQ,IAAI,IAAI;QAErB,aAAa;QACb,IAAI;YACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,WAAW,QAAQ,IAAI;YAC/C,IAAI,CAAC,OAAO;gBACV,WAAW;YACb;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,wBAAwB;QACxC,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,6LAAC;gBAAK,UAAU;;kCACd,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCAAC,WAAU;;kDAChB,6LAAC,qIAAA,CAAA,cAAW;wCAAC,KAAK,SAAS,cAAc;;;;;;kDACzC,6LAAC,qIAAA,CAAA,iBAAc;kDACZ,SAAS,WAAW,OAAO,MAAM,KAAK,KAAK,EAAE,OAAO,MAAM;;;;;;;;;;;;0CAG/D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,uIAAA,CAAA,WAAQ;oCACP,aAAa,CAAC,qBAAqB,EAAE,SAAS,WAAW,MAAM,IAAI,CAAC,EAAE,IAAI,QAAQ,CAAC,CAAC;oCACpF,OAAO;oCACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;oCAC1C,WAAU;;;;;;;;;;;;;;;;;kCAKhB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;;8DAEV,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAgC;;;;;;;sDAGnD,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;;8DAEV,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiC;;;;;;;sDAGpD,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;;8DAEV,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAA8B;;;;;;;;;;;;;8CAIpD,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU,CAAC,QAAQ,IAAI,MAAM;oCAC7B,WAAU;8CAET,YAAY,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5C;GA1FgB;;QACY,wIAAA,CAAA,eAAY;QACf,yIAAA,CAAA,gBAAa;;;KAFtB"}}, {"offset": {"line": 2028, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2034, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/fbaug/facebook-clone/src/components/posts/post-feed.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { PostCard } from './post-card'\nimport { CreatePost } from './create-post'\nimport { usePostsStore } from '@/lib/stores/posts-store'\nimport { useAuthStore } from '@/lib/stores/auth-store'\n\nexport function PostFeed() {\n  const { posts, loading, fetchPosts } = usePostsStore()\n  const { user } = useAuthStore()\n\n  useEffect(() => {\n    if (user) {\n      fetchPosts()\n    }\n  }, [user, fetchPosts])\n\n  if (!user) {\n    return null\n  }\n\n  return (\n    <div className=\"max-w-2xl mx-auto\">\n      <CreatePost />\n      \n      {loading ? (\n        <div className=\"space-y-4\">\n          {[...Array(3)].map((_, i) => (\n            <div key={i} className=\"bg-white rounded-lg shadow p-4 animate-pulse\">\n              <div className=\"flex items-center space-x-3 mb-4\">\n                <div className=\"w-10 h-10 bg-gray-300 rounded-full\"></div>\n                <div className=\"space-y-2\">\n                  <div className=\"h-4 bg-gray-300 rounded w-32\"></div>\n                  <div className=\"h-3 bg-gray-300 rounded w-24\"></div>\n                </div>\n              </div>\n              <div className=\"space-y-2\">\n                <div className=\"h-4 bg-gray-300 rounded\"></div>\n                <div className=\"h-4 bg-gray-300 rounded w-3/4\"></div>\n              </div>\n            </div>\n          ))}\n        </div>\n      ) : posts.length === 0 ? (\n        <div className=\"text-center py-12\">\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No posts yet</h3>\n          <p className=\"text-gray-600\">Be the first to share something!</p>\n        </div>\n      ) : (\n        <div className=\"space-y-4\">\n          {posts.map((post) => (\n            <PostCard key={post.id} post={post} />\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQO,SAAS;;IACd,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD;IACnD,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAE5B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,MAAM;gBACR;YACF;QACF;6BAAG;QAAC;QAAM;KAAW;IAErB,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,gJAAA,CAAA,aAAU;;;;;YAEV,wBACC,6LAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;wBAAY,WAAU;;0CACrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;;;;;;;0CAGnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;uBAVT;;;;;;;;;uBAeZ,MAAM,MAAM,KAAK,kBACnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;qCAG/B,6LAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC,8IAAA,CAAA,WAAQ;wBAAe,MAAM;uBAAf,KAAK,EAAE;;;;;;;;;;;;;;;;AAMlC;GAlDgB;;QACyB,yIAAA,CAAA,gBAAa;QACnC,wIAAA,CAAA,eAAY;;;KAFf"}}, {"offset": {"line": 2212, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 2218, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/fbaug/facebook-clone/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { Navbar } from '@/components/layout/navbar'\nimport { PostFeed } from '@/components/posts/post-feed'\nimport { useAuthStore } from '@/lib/stores/auth-store'\n\nexport default function Home() {\n  const router = useRouter()\n  const { user, loading } = useAuthStore()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/login')\n    }\n  }, [user, loading, router])\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      <Navbar />\n      <main className=\"container mx-auto px-4 py-6\">\n        <PostFeed />\n      </main>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,eAAY,AAAD;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;yBAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,yIAAA,CAAA,SAAM;;;;;0BACP,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,8IAAA,CAAA,WAAQ;;;;;;;;;;;;;;;;AAIjB;GA9BwB;;QACP,qIAAA,CAAA,YAAS;QACE,wIAAA,CAAA,eAAY;;;KAFhB"}}, {"offset": {"line": 2308, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}