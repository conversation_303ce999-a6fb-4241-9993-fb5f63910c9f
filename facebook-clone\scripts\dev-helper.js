#!/usr/bin/env node

// Facebook Clone Development Helper
// Quick commands for development and testing

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const commands = {
  'test-supabase': {
    description: 'Test Supabase connection and setup',
    command: 'node scripts/test-supabase.js'
  },
  'add-demo': {
    description: 'Add demo data to the database',
    command: 'node scripts/add-demo-data.js'
  },
  'dev': {
    description: 'Start development server',
    command: 'npm run dev'
  },
  'build': {
    description: 'Build for production',
    command: 'npm run build'
  },
  'lint': {
    description: 'Run ESLint',
    command: 'npm run lint'
  },
  'check-env': {
    description: 'Check environment variables',
    command: 'node -e "console.log(\'Supabase URL:\', process.env.NEXT_PUBLIC_SUPABASE_URL); console.log(\'Supabase Key:\', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? \'Set\' : \'Not set\');"'
  }
};

function showHelp() {
  console.log('🚀 Facebook Clone Development Helper\n');
  console.log('Available commands:\n');
  
  Object.entries(commands).forEach(([cmd, info]) => {
    console.log(`  ${cmd.padEnd(15)} - ${info.description}`);
  });
  
  console.log('\nUsage:');
  console.log('  node scripts/dev-helper.js <command>');
  console.log('  npm run helper <command>\n');
  
  console.log('Quick setup:');
  console.log('  1. Set up Supabase credentials in .env.local');
  console.log('  2. Run: node scripts/dev-helper.js test-supabase');
  console.log('  3. Run: node scripts/dev-helper.js add-demo');
  console.log('  4. Run: node scripts/dev-helper.js dev\n');
}

function runCommand(cmd) {
  if (!commands[cmd]) {
    console.log(`❌ Unknown command: ${cmd}\n`);
    showHelp();
    return;
  }
  
  console.log(`🔄 Running: ${commands[cmd].description}...\n`);
  
  try {
    execSync(commands[cmd].command, { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
  } catch (error) {
    console.log(`\n❌ Command failed with exit code: ${error.status}`);
  }
}

// Main execution
const command = process.argv[2];

if (!command) {
  showHelp();
} else {
  runCommand(command);
}
