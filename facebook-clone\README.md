# 🚀 Facebook Clone - Modern Social Media Platform

A highly scalable Facebook clone built with modern technologies including Next.js 14, Supabase, and Tailwind CSS. This application demonstrates best practices for building social media platforms with real-time features, authentication, and responsive design.

## ✨ Features

### 🔐 Authentication System
- **User Registration & Login** - Secure authentication with Supabase Auth
- **Email/Password Authentication** - Traditional login system
- **Protected Routes** - Automatic redirection for unauthenticated users
- **Session Management** - Persistent login sessions

### 📱 Social Features
- **News Feed** - Infinite scroll feed with real-time updates
- **Post Creation** - Rich text posts with image support
- **Social Interactions** - Like, comment, and share functionality
- **User Profiles** - Customizable user profiles with avatars
- **Friend System** - Send and accept friend requests
- **Real-time Chat** - Direct messaging between users

### 🎨 Modern UI/UX
- **Responsive Design** - Mobile-first approach with Tailwind CSS
- **shadcn/ui Components** - Beautiful, accessible UI components
- **Loading States** - Smooth loading animations and skeletons
- **Error Handling** - Graceful error boundaries and user feedback

### ⚡ Performance & Scalability
- **Next.js 14 App Router** - Latest Next.js features with server components
- **Real-time Updates** - Supabase Realtime for live data synchronization
- **Optimistic Updates** - Instant UI feedback for better UX
- **State Management** - Zustand for lightweight, efficient state management

## 🛠️ Technology Stack

### Frontend
- **Next.js 14** - React framework with App Router
- **React 18** - Latest React features with concurrent rendering
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **shadcn/ui** - Modern, accessible component library
- **Lucide React** - Beautiful icon library

### Backend & Database
- **Supabase** - Backend-as-a-Service platform
  - PostgreSQL database
  - Real-time subscriptions
  - Authentication & authorization
  - File storage
  - Row Level Security (RLS)

### State Management & Forms
- **Zustand** - Lightweight state management
- **React Hook Form** - Performant form handling
- **Zod** - Schema validation

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- Supabase account (free tier available)

### 1. Clone the Repository
```bash
git clone <repository-url>
cd facebook-clone
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Set Up Supabase

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your project URL and anon key
3. Update `.env.local` with your Supabase credentials:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### 4. Run the Development Server
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

## 📁 Project Structure

```
facebook-clone/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── login/             # Login page
│   │   ├── register/          # Registration page
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Home page
│   ├── components/            # React components
│   │   ├── auth/              # Authentication components
│   │   ├── layout/            # Layout components
│   │   ├── posts/             # Post-related components
│   │   ├── providers/         # Context providers
│   │   └── ui/                # shadcn/ui components
│   └── lib/                   # Utility functions and configurations
│       ├── stores/            # Zustand stores
│       ├── supabase.ts        # Supabase client
│       └── utils.ts           # Utility functions
├── .env.local                 # Environment variables
├── next.config.js             # Next.js configuration
├── tailwind.config.js         # Tailwind CSS configuration
└── package.json               # Dependencies and scripts
```

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Add environment variables in Vercel dashboard
4. Deploy automatically on every push

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - The React framework for production
- [Supabase](https://supabase.com/) - The open source Firebase alternative
- [Tailwind CSS](https://tailwindcss.com/) - A utility-first CSS framework
- [shadcn/ui](https://ui.shadcn.com/) - Beautifully designed components
- [Lucide](https://lucide.dev/) - Beautiful & consistent icon toolkit

---

Built with ❤️ using modern web technologies
