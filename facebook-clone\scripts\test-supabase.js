// Test Supabase Connection
// Run this with: node scripts/test-supabase.js

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

console.log('🔍 Testing Supabase Connection...\n');

if (!supabaseUrl || supabaseUrl.includes('your-project-id')) {
  console.log('❌ NEXT_PUBLIC_SUPABASE_URL is not set correctly');
  console.log('   Current value:', supabaseUrl);
  console.log('   Expected format: https://your-project-id.supabase.co\n');
  process.exit(1);
}

if (!supabaseKey || supabaseKey.includes('your_actual_anon_key')) {
  console.log('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY is not set correctly');
  console.log('   Current value:', supabaseKey ? supabaseKey.substring(0, 20) + '...' : 'undefined');
  console.log('   Expected: A long JWT token starting with eyJhbGciOiJIUzI1NiIs...\n');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testConnection() {
  try {
    console.log('✅ Environment variables look good!');
    console.log('   URL:', supabaseUrl);
    console.log('   Key:', supabaseKey.substring(0, 20) + '...\n');

    // Test basic connection
    console.log('🔗 Testing database connection...');
    const { data, error } = await supabase.from('profiles').select('count').limit(1);
    
    if (error) {
      console.log('❌ Database connection failed:', error.message);
      console.log('   Make sure you ran the database schema in Supabase SQL Editor\n');
      return;
    }

    console.log('✅ Database connection successful!\n');

    // Test authentication
    console.log('🔐 Testing authentication...');
    const { data: authData, error: authError } = await supabase.auth.getSession();
    
    if (authError) {
      console.log('❌ Auth test failed:', authError.message);
      return;
    }

    console.log('✅ Authentication service is working!\n');

    // Check if tables exist
    console.log('📊 Checking database tables...');
    const tables = ['profiles', 'posts', 'likes', 'comments', 'friendships'];
    
    for (const table of tables) {
      try {
        const { error } = await supabase.from(table).select('*').limit(1);
        if (error) {
          console.log(`❌ Table '${table}' not found or accessible`);
        } else {
          console.log(`✅ Table '${table}' exists and accessible`);
        }
      } catch (err) {
        console.log(`❌ Error checking table '${table}':`, err.message);
      }
    }

    console.log('\n🎉 Supabase setup is complete and working!');
    console.log('   You can now use your Facebook clone application.');
    console.log('   Visit: http://localhost:3000\n');

  } catch (error) {
    console.log('❌ Unexpected error:', error.message);
  }
}

testConnection();
