import { create } from 'zustand'
import { supabase } from '@/lib/supabase'

interface Post {
  id: string
  user_id: string
  content: string
  image_url: string | null
  likes_count: number
  comments_count: number
  created_at: string
  updated_at: string
  profiles: {
    full_name: string | null
    avatar_url: string | null
  }
  user_has_liked?: boolean
}

interface Comment {
  id: string
  user_id: string
  post_id: string
  content: string
  created_at: string
  updated_at: string
  profiles: {
    full_name: string | null
    avatar_url: string | null
  }
}

interface PostsState {
  posts: Post[]
  loading: boolean
  comments: { [postId: string]: Comment[] }
  fetchPosts: () => Promise<void>
  createPost: (content: string, imageUrl?: string) => Promise<{ error: any }>
  likePost: (postId: string) => Promise<{ error: any }>
  unlikePost: (postId: string) => Promise<{ error: any }>
  addComment: (postId: string, content: string) => Promise<{ error: any }>
  fetchComments: (postId: string) => Promise<void>
}

export const usePostsStore = create<PostsState>((set, get) => ({
  posts: [],
  loading: false,
  comments: {},

  fetchPosts: async () => {
    set({ loading: true })
    try {
      const { data: posts, error } = await supabase
        .from('posts')
        .select(`
          *,
          profiles:user_id (
            full_name,
            avatar_url
          )
        `)
        .order('created_at', { ascending: false })

      if (error) throw error

      // Check which posts the current user has liked
      const { data: { user } } = await supabase.auth.getUser()
      if (user && posts) {
        const { data: likes } = await supabase
          .from('likes')
          .select('post_id')
          .eq('user_id', user.id)

        const likedPostIds = new Set(likes?.map(like => like.post_id) || [])
        
        const postsWithLikes = posts.map(post => ({
          ...post,
          user_has_liked: likedPostIds.has(post.id)
        }))

        set({ posts: postsWithLikes })
      } else {
        set({ posts: posts || [] })
      }
    } catch (error) {
      console.error('Error fetching posts:', error)
    } finally {
      set({ loading: false })
    }
  },

  createPost: async (content: string, imageUrl?: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return { error: 'Not authenticated' }

      const { data, error } = await supabase
        .from('posts')
        .insert({
          user_id: user.id,
          content,
          image_url: imageUrl || null,
        })
        .select(`
          *,
          profiles:user_id (
            full_name,
            avatar_url
          )
        `)
        .single()

      if (error) return { error }

      // Add the new post to the beginning of the posts array
      const { posts } = get()
      set({ posts: [{ ...data, user_has_liked: false }, ...posts] })

      return { error: null }
    } catch (error) {
      return { error }
    }
  },

  likePost: async (postId: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return { error: 'Not authenticated' }

      // Add like
      const { error: likeError } = await supabase
        .from('likes')
        .insert({
          user_id: user.id,
          post_id: postId,
        })

      if (likeError) return { error: likeError }

      // Update likes count
      const { error: updateError } = await supabase.rpc('increment_likes', {
        post_id: postId
      })

      if (updateError) return { error: updateError }

      // Update local state
      const { posts } = get()
      const updatedPosts = posts.map(post =>
        post.id === postId
          ? { ...post, likes_count: post.likes_count + 1, user_has_liked: true }
          : post
      )
      set({ posts: updatedPosts })

      return { error: null }
    } catch (error) {
      return { error }
    }
  },

  unlikePost: async (postId: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return { error: 'Not authenticated' }

      // Remove like
      const { error: unlikeError } = await supabase
        .from('likes')
        .delete()
        .eq('user_id', user.id)
        .eq('post_id', postId)

      if (unlikeError) return { error: unlikeError }

      // Update likes count
      const { error: updateError } = await supabase.rpc('decrement_likes', {
        post_id: postId
      })

      if (updateError) return { error: updateError }

      // Update local state
      const { posts } = get()
      const updatedPosts = posts.map(post =>
        post.id === postId
          ? { ...post, likes_count: Math.max(0, post.likes_count - 1), user_has_liked: false }
          : post
      )
      set({ posts: updatedPosts })

      return { error: null }
    } catch (error) {
      return { error }
    }
  },

  addComment: async (postId: string, content: string) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) return { error: 'Not authenticated' }

      const { data, error } = await supabase
        .from('comments')
        .insert({
          user_id: user.id,
          post_id: postId,
          content,
        })
        .select(`
          *,
          profiles:user_id (
            full_name,
            avatar_url
          )
        `)
        .single()

      if (error) return { error }

      // Update comments count
      await supabase.rpc('increment_comments', { post_id: postId })

      // Update local state
      const { posts, comments } = get()
      const updatedPosts = posts.map(post =>
        post.id === postId
          ? { ...post, comments_count: post.comments_count + 1 }
          : post
      )
      const updatedComments = {
        ...comments,
        [postId]: [...(comments[postId] || []), data]
      }
      set({ posts: updatedPosts, comments: updatedComments })

      return { error: null }
    } catch (error) {
      return { error }
    }
  },

  fetchComments: async (postId: string) => {
    try {
      const { data, error } = await supabase
        .from('comments')
        .select(`
          *,
          profiles:user_id (
            full_name,
            avatar_url
          )
        `)
        .eq('post_id', postId)
        .order('created_at', { ascending: true })

      if (error) throw error

      const { comments } = get()
      set({
        comments: {
          ...comments,
          [postId]: data || []
        }
      })
    } catch (error) {
      console.error('Error fetching comments:', error)
    }
  },
}))
