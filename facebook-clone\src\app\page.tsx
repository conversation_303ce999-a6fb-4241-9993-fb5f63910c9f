'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Navbar } from '@/components/layout/navbar'
import { PostFeed } from '@/components/posts/post-feed'
import { SetupNotification } from '@/components/setup/setup-notification'
import { useAuthStore } from '@/lib/stores/auth-store'
import { isSupabaseConfigured } from '@/lib/supabase'

export default function Home() {
  const router = useRouter()
  const { user, loading } = useAuthStore()

  // Show setup notification if Supabase isn't configured
  if (!isSupabaseConfigured()) {
    return <SetupNotification />
  }

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <main className="container mx-auto px-4 py-6">
        <PostFeed />
      </main>
    </div>
  )
}
