// Add Demo Data to Facebook Clone
// Run this after setting up Supabase: node scripts/add-demo-data.js

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
);

const demoData = {
  posts: [
    {
      content: "🎉 Welcome to our Facebook Clone! This is a modern social media platform built with Next.js 14, Supabase, and Tailwind CSS. Feel free to like, comment, and share your thoughts!",
      user_id: "demo-user-1"
    },
    {
      content: "Just finished building this amazing social platform! 🚀 The tech stack includes:\n\n✅ Next.js 14 with App Router\n✅ Supabase for backend\n✅ Tailwind CSS + shadcn/ui\n✅ Real-time updates\n✅ TypeScript for type safety\n\nWhat do you think? Drop a comment below! 👇",
      user_id: "demo-user-1"
    },
    {
      content: "The future of social media is here! 🌟 This platform features:\n\n🔐 Secure authentication\n📱 Mobile-responsive design\n⚡ Real-time synchronization\n💬 Interactive comments\n❤️ Social reactions\n\nTry creating your own post and see the magic happen! ✨",
      user_id: "demo-user-1"
    },
    {
      content: "Pro tip: This Facebook clone is built with modern web technologies that ensure:\n\n🚀 Lightning-fast performance\n🔒 Enterprise-grade security\n📈 Infinite scalability\n🎨 Beautiful user experience\n\nPerfect for learning or building your own social platform! 💡",
      user_id: "demo-user-1"
    }
  ],
  comments: [
    {
      content: "This is incredible! The UI looks so clean and modern. 😍",
      post_index: 0
    },
    {
      content: "Love the real-time features! Everything updates instantly. ⚡",
      post_index: 0
    },
    {
      content: "The tech stack is impressive! Next.js + Supabase is a powerful combination. 🔥",
      post_index: 1
    },
    {
      content: "Can't wait to see what other features you'll add! 🚀",
      post_index: 1
    },
    {
      content: "The responsive design works perfectly on mobile! 📱",
      post_index: 2
    },
    {
      content: "This is exactly what modern social media should look like! 👏",
      post_index: 3
    }
  ]
};

async function addDemoData() {
  console.log('🎯 Adding demo data to your Facebook clone...\n');

  try {
    // Check if we can connect
    const { data: testData, error: testError } = await supabase
      .from('posts')
      .select('count')
      .limit(1);

    if (testError) {
      console.log('❌ Cannot connect to database. Make sure Supabase is set up correctly.');
      console.log('   Error:', testError.message);
      return;
    }

    // Check if demo data already exists
    const { data: existingPosts } = await supabase
      .from('posts')
      .select('*')
      .ilike('content', '%Welcome to our Facebook Clone%');

    if (existingPosts && existingPosts.length > 0) {
      console.log('ℹ️  Demo data already exists. Skipping...');
      console.log('   Found', existingPosts.length, 'existing demo posts.\n');
      return;
    }

    // Create a demo user profile (using a fixed UUID for consistency)
    const demoUserId = '********-0000-0000-0000-********0001';
    
    console.log('👤 Creating demo user profile...');
    const { error: profileError } = await supabase
      .from('profiles')
      .upsert({
        id: demoUserId,
        email: '<EMAIL>',
        full_name: 'Facebook Clone Demo',
        bio: 'Official demo account for the Facebook Clone platform. Showcasing the amazing features of this modern social media app! 🚀',
        avatar_url: null
      });

    if (profileError && !profileError.message.includes('duplicate')) {
      console.log('⚠️  Could not create demo profile:', profileError.message);
    } else {
      console.log('✅ Demo user profile created successfully!');
    }

    // Add demo posts
    console.log('📝 Adding demo posts...');
    const insertedPosts = [];
    
    for (const post of demoData.posts) {
      const { data, error } = await supabase
        .from('posts')
        .insert({
          ...post,
          user_id: demoUserId
        })
        .select()
        .single();

      if (error) {
        console.log('❌ Error adding post:', error.message);
      } else {
        insertedPosts.push(data);
        console.log('✅ Added post:', data.content.substring(0, 50) + '...');
      }
    }

    // Add demo comments
    console.log('\n💬 Adding demo comments...');
    for (const comment of demoData.comments) {
      const targetPost = insertedPosts[comment.post_index];
      if (targetPost) {
        const { error } = await supabase
          .from('comments')
          .insert({
            content: comment.content,
            post_id: targetPost.id,
            user_id: demoUserId
          });

        if (error) {
          console.log('❌ Error adding comment:', error.message);
        } else {
          console.log('✅ Added comment to post');
        }
      }
    }

    // Add some demo likes
    console.log('\n❤️  Adding demo likes...');
    for (const post of insertedPosts) {
      const { error } = await supabase
        .from('likes')
        .insert({
          post_id: post.id,
          user_id: demoUserId
        });

      if (error && !error.message.includes('duplicate')) {
        console.log('❌ Error adding like:', error.message);
      }
    }

    // Update post counts
    console.log('\n🔄 Updating post statistics...');
    for (const post of insertedPosts) {
      // Update likes count
      await supabase.rpc('increment_likes', { post_id: post.id });
      
      // Update comments count
      const commentsForPost = demoData.comments.filter(c => 
        insertedPosts[c.post_index]?.id === post.id
      ).length;
      
      for (let i = 0; i < commentsForPost; i++) {
        await supabase.rpc('increment_comments', { post_id: post.id });
      }
    }

    console.log('\n🎉 Demo data added successfully!');
    console.log('📊 Summary:');
    console.log(`   • ${insertedPosts.length} demo posts created`);
    console.log(`   • ${demoData.comments.length} demo comments added`);
    console.log(`   • ${insertedPosts.length} demo likes added`);
    console.log('\n🚀 Your Facebook clone is now ready with sample content!');
    console.log('   Visit: http://localhost:3000\n');

  } catch (error) {
    console.log('❌ Unexpected error:', error.message);
  }
}

addDemoData();
