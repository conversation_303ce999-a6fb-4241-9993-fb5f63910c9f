{"version": 3, "sources": ["../src/use-layout-effect.tsx"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * On the server, <PERSON>act emits a warning when calling `useLayoutEffect`.\n * This is because neither `useLayoutEffect` nor `useEffect` run on the server.\n * We use this safe version which suppresses the warning by replacing it with a noop on the server.\n *\n * See: https://reactjs.org/docs/hooks-reference.html#uselayouteffect\n */\nconst useLayoutEffect = globalThis?.document ? React.useLayoutEffect : () => {};\n\nexport { useLayoutEffect };\n"], "mappings": ";AAAA,YAAY,WAAW;AASvB,IAAMA,mBAAkB,YAAY,WAAiB,wBAAkB,MAAM;AAAC;", "names": ["useLayoutEffect"]}