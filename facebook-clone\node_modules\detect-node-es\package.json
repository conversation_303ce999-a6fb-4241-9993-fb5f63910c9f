{"name": "detect-node-es", "version": "1.1.0", "description": "Detect Node.JS (as opposite to browser environment). ESM modification", "main": "es5/node.js", "module": "esm/node.js", "browser": {"./es5/node.js": "./es5/browser.js", "./esm/node.js": "./esm/browser.js"}, "types": "es5/node.d.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/thekashey/detect-node"}, "keywords": ["detect", "node"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/thekashey/detect-node/issues"}, "homepage": "https://github.com/thekashey/detect-node"}