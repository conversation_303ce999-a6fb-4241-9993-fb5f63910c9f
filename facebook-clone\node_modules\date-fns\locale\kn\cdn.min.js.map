{"version": 3, "sources": ["lib/locale/kn/cdn.js"], "sourcesContent": ["(() => {\nvar _window$dateFns;function _typeof(o) {\"@babel/helpers - typeof\";return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {return typeof o;} : function (o) {return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;}, _typeof(o);}function ownKeys(e, r) {var t = Object.keys(e);if (Object.getOwnPropertySymbols) {var o = Object.getOwnPropertySymbols(e);r && (o = o.filter(function (r) {return Object.getOwnPropertyDescriptor(e, r).enumerable;})), t.push.apply(t, o);}return t;}function _objectSpread(e) {for (var r = 1; r < arguments.length; r++) {var t = null != arguments[r] ? arguments[r] : {};r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {_defineProperty(e, r, t[r]);}) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));});}return e;}function _defineProperty(obj, key, value) {key = _toPropertyKey(key);if (key in obj) {Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true });} else {obj[key] = value;}return obj;}function _toPropertyKey(t) {var i = _toPrimitive(t, \"string\");return \"symbol\" == _typeof(i) ? i : String(i);}function _toPrimitive(t, r) {if (\"object\" != _typeof(t) || !t) return t;var e = t[Symbol.toPrimitive];if (void 0 !== e) {var i = e.call(t, r || \"default\");if (\"object\" != _typeof(i)) return i;throw new TypeError(\"@@toPrimitive must return a primitive value.\");}return (\"string\" === r ? String : Number)(t);}var __defProp = Object.defineProperty;\nvar __export = function __export(target, all) {\n  for (var name in all)\n  __defProp(target, name, {\n    get: all[name],\n    enumerable: true,\n    configurable: true,\n    set: function set(newValue) {return all[name] = function () {return newValue;};}\n  });\n};\n\n// lib/locale/kn/_lib/formatDistance.js\nfunction getResultByTense(parentToken, options) {\n  if (options !== null && options !== void 0 && options.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return parentToken.future;\n    } else {\n      return parentToken.past;\n    }\n  }\n  return parentToken.default;\n}\nvar formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: {\n      default: \"1 \\u0CB8\\u0CC6\\u0C95\\u0CC6\\u0C82\\u0CA1\\u0CCD\\u200C\\u0C97\\u0CBF\\u0C82\\u0CA4 \\u0C95\\u0CA1\\u0CBF\\u0CAE\\u0CC6\",\n      future: \"1 \\u0CB8\\u0CC6\\u0C95\\u0CC6\\u0C82\\u0CA1\\u0CCD\\u200C\\u0C97\\u0CBF\\u0C82\\u0CA4 \\u0C95\\u0CA1\\u0CBF\\u0CAE\\u0CC6\",\n      past: \"1 \\u0CB8\\u0CC6\\u0C95\\u0CC6\\u0C82\\u0CA1\\u0CCD\\u200C\\u0C97\\u0CBF\\u0C82\\u0CA4 \\u0C95\\u0CA1\\u0CBF\\u0CAE\\u0CC6\"\n    },\n    other: {\n      default: \"{{count}} \\u0CB8\\u0CC6\\u0C95\\u0CC6\\u0C82\\u0CA1\\u0CCD\\u200C\\u0C97\\u0CBF\\u0C82\\u0CA4 \\u0C95\\u0CA1\\u0CBF\\u0CAE\\u0CC6\",\n      future: \"{{count}} \\u0CB8\\u0CC6\\u0C95\\u0CC6\\u0C82\\u0CA1\\u0CCD\\u200C\\u0C97\\u0CBF\\u0C82\\u0CA4 \\u0C95\\u0CA1\\u0CBF\\u0CAE\\u0CC6\",\n      past: \"{{count}} \\u0CB8\\u0CC6\\u0C95\\u0CC6\\u0C82\\u0CA1\\u0CCD\\u200C\\u0C97\\u0CBF\\u0C82\\u0CA4 \\u0C95\\u0CA1\\u0CBF\\u0CAE\\u0CC6\"\n    }\n  },\n  xSeconds: {\n    one: {\n      default: \"1 \\u0CB8\\u0CC6\\u0C95\\u0CC6\\u0C82\\u0CA1\\u0CCD\",\n      future: \"1 \\u0CB8\\u0CC6\\u0C95\\u0CC6\\u0C82\\u0CA1\\u0CCD\\u200C\\u0CA8\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      past: \"1 \\u0CB8\\u0CC6\\u0C95\\u0CC6\\u0C82\\u0CA1\\u0CCD \\u0CB9\\u0CBF\\u0C82\\u0CA6\\u0CC6\"\n    },\n    other: {\n      default: \"{{count}} \\u0CB8\\u0CC6\\u0C95\\u0CC6\\u0C82\\u0CA1\\u0CC1\\u0C97\\u0CB3\\u0CC1\",\n      future: \"{{count}} \\u0CB8\\u0CC6\\u0C95\\u0CC6\\u0C82\\u0CA1\\u0CCD\\u200C\\u0C97\\u0CB3\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      past: \"{{count}} \\u0CB8\\u0CC6\\u0C95\\u0CC6\\u0C82\\u0CA1\\u0CCD \\u0CB9\\u0CBF\\u0C82\\u0CA6\\u0CC6\"\n    }\n  },\n  halfAMinute: {\n    other: {\n      default: \"\\u0C85\\u0CB0\\u0CCD\\u0CA7 \\u0CA8\\u0CBF\\u0CAE\\u0CBF\\u0CB7\",\n      future: \"\\u0C85\\u0CB0\\u0CCD\\u0CA7 \\u0CA8\\u0CBF\\u0CAE\\u0CBF\\u0CB7\\u0CA6\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      past: \"\\u0C85\\u0CB0\\u0CCD\\u0CA7 \\u0CA8\\u0CBF\\u0CAE\\u0CBF\\u0CB7\\u0CA6 \\u0CB9\\u0CBF\\u0C82\\u0CA6\\u0CC6\"\n    }\n  },\n  lessThanXMinutes: {\n    one: {\n      default: \"1 \\u0CA8\\u0CBF\\u0CAE\\u0CBF\\u0CB7\\u0C95\\u0CCD\\u0C95\\u0CBF\\u0C82\\u0CA4 \\u0C95\\u0CA1\\u0CBF\\u0CAE\\u0CC6\",\n      future: \"1 \\u0CA8\\u0CBF\\u0CAE\\u0CBF\\u0CB7\\u0C95\\u0CCD\\u0C95\\u0CBF\\u0C82\\u0CA4 \\u0C95\\u0CA1\\u0CBF\\u0CAE\\u0CC6\",\n      past: \"1 \\u0CA8\\u0CBF\\u0CAE\\u0CBF\\u0CB7\\u0C95\\u0CCD\\u0C95\\u0CBF\\u0C82\\u0CA4 \\u0C95\\u0CA1\\u0CBF\\u0CAE\\u0CC6\"\n    },\n    other: {\n      default: \"{{count}} \\u0CA8\\u0CBF\\u0CAE\\u0CBF\\u0CB7\\u0C95\\u0CCD\\u0C95\\u0CBF\\u0C82\\u0CA4 \\u0C95\\u0CA1\\u0CBF\\u0CAE\\u0CC6\",\n      future: \"{{count}} \\u0CA8\\u0CBF\\u0CAE\\u0CBF\\u0CB7\\u0C95\\u0CCD\\u0C95\\u0CBF\\u0C82\\u0CA4 \\u0C95\\u0CA1\\u0CBF\\u0CAE\\u0CC6\",\n      past: \"{{count}} \\u0CA8\\u0CBF\\u0CAE\\u0CBF\\u0CB7\\u0C95\\u0CCD\\u0C95\\u0CBF\\u0C82\\u0CA4 \\u0C95\\u0CA1\\u0CBF\\u0CAE\\u0CC6\"\n    }\n  },\n  xMinutes: {\n    one: {\n      default: \"1 \\u0CA8\\u0CBF\\u0CAE\\u0CBF\\u0CB7\",\n      future: \"1 \\u0CA8\\u0CBF\\u0CAE\\u0CBF\\u0CB7\\u0CA6\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      past: \"1 \\u0CA8\\u0CBF\\u0CAE\\u0CBF\\u0CB7\\u0CA6 \\u0CB9\\u0CBF\\u0C82\\u0CA6\\u0CC6\"\n    },\n    other: {\n      default: \"{{count}} \\u0CA8\\u0CBF\\u0CAE\\u0CBF\\u0CB7\\u0C97\\u0CB3\\u0CC1\",\n      future: \"{{count}} \\u0CA8\\u0CBF\\u0CAE\\u0CBF\\u0CB7\\u0C97\\u0CB3\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      past: \"{{count}} \\u0CA8\\u0CBF\\u0CAE\\u0CBF\\u0CB7\\u0C97\\u0CB3 \\u0CB9\\u0CBF\\u0C82\\u0CA6\\u0CC6\"\n    }\n  },\n  aboutXHours: {\n    one: {\n      default: \"\\u0CB8\\u0CC1\\u0CAE\\u0CBE\\u0CB0\\u0CC1 1 \\u0C97\\u0C82\\u0C9F\\u0CC6\",\n      future: \"\\u0CB8\\u0CC1\\u0CAE\\u0CBE\\u0CB0\\u0CC1 1 \\u0C97\\u0C82\\u0C9F\\u0CC6\\u0CAF\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      past: \"\\u0CB8\\u0CC1\\u0CAE\\u0CBE\\u0CB0\\u0CC1 1 \\u0C97\\u0C82\\u0C9F\\u0CC6 \\u0CB9\\u0CBF\\u0C82\\u0CA6\\u0CC6\"\n    },\n    other: {\n      default: \"\\u0CB8\\u0CC1\\u0CAE\\u0CBE\\u0CB0\\u0CC1 {{count}} \\u0C97\\u0C82\\u0C9F\\u0CC6\\u0C97\\u0CB3\\u0CC1\",\n      future: \"\\u0CB8\\u0CC1\\u0CAE\\u0CBE\\u0CB0\\u0CC1 {{count}} \\u0C97\\u0C82\\u0C9F\\u0CC6\\u0C97\\u0CB3\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      past: \"\\u0CB8\\u0CC1\\u0CAE\\u0CBE\\u0CB0\\u0CC1 {{count}} \\u0C97\\u0C82\\u0C9F\\u0CC6\\u0C97\\u0CB3 \\u0CB9\\u0CBF\\u0C82\\u0CA6\\u0CC6\"\n    }\n  },\n  xHours: {\n    one: {\n      default: \"1 \\u0C97\\u0C82\\u0C9F\\u0CC6\",\n      future: \"1 \\u0C97\\u0C82\\u0C9F\\u0CC6\\u0CAF\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      past: \"1 \\u0C97\\u0C82\\u0C9F\\u0CC6 \\u0CB9\\u0CBF\\u0C82\\u0CA6\\u0CC6\"\n    },\n    other: {\n      default: \"{{count}} \\u0C97\\u0C82\\u0C9F\\u0CC6\\u0C97\\u0CB3\\u0CC1\",\n      future: \"{{count}} \\u0C97\\u0C82\\u0C9F\\u0CC6\\u0C97\\u0CB3\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      past: \"{{count}} \\u0C97\\u0C82\\u0C9F\\u0CC6\\u0C97\\u0CB3 \\u0CB9\\u0CBF\\u0C82\\u0CA6\\u0CC6\"\n    }\n  },\n  xDays: {\n    one: {\n      default: \"1 \\u0CA6\\u0CBF\\u0CA8\",\n      future: \"1 \\u0CA6\\u0CBF\\u0CA8\\u0CA6\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      past: \"1 \\u0CA6\\u0CBF\\u0CA8\\u0CA6 \\u0CB9\\u0CBF\\u0C82\\u0CA6\\u0CC6\"\n    },\n    other: {\n      default: \"{{count}} \\u0CA6\\u0CBF\\u0CA8\\u0C97\\u0CB3\\u0CC1\",\n      future: \"{{count}} \\u0CA6\\u0CBF\\u0CA8\\u0C97\\u0CB3\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      past: \"{{count}} \\u0CA6\\u0CBF\\u0CA8\\u0C97\\u0CB3 \\u0CB9\\u0CBF\\u0C82\\u0CA6\\u0CC6\"\n    }\n  },\n  aboutXMonths: {\n    one: {\n      default: \"\\u0CB8\\u0CC1\\u0CAE\\u0CBE\\u0CB0\\u0CC1 1 \\u0CA4\\u0CBF\\u0C82\\u0C97\\u0CB3\\u0CC1\",\n      future: \"\\u0CB8\\u0CC1\\u0CAE\\u0CBE\\u0CB0\\u0CC1 1 \\u0CA4\\u0CBF\\u0C82\\u0C97\\u0CB3\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      past: \"\\u0CB8\\u0CC1\\u0CAE\\u0CBE\\u0CB0\\u0CC1 1 \\u0CA4\\u0CBF\\u0C82\\u0C97\\u0CB3 \\u0CB9\\u0CBF\\u0C82\\u0CA6\\u0CC6\"\n    },\n    other: {\n      default: \"\\u0CB8\\u0CC1\\u0CAE\\u0CBE\\u0CB0\\u0CC1 {{count}} \\u0CA4\\u0CBF\\u0C82\\u0C97\\u0CB3\\u0CC1\",\n      future: \"\\u0CB8\\u0CC1\\u0CAE\\u0CBE\\u0CB0\\u0CC1 {{count}} \\u0CA4\\u0CBF\\u0C82\\u0C97\\u0CB3\\u0CC1\\u0C97\\u0CB3\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      past: \"\\u0CB8\\u0CC1\\u0CAE\\u0CBE\\u0CB0\\u0CC1 {{count}} \\u0CA4\\u0CBF\\u0C82\\u0C97\\u0CB3\\u0CC1\\u0C97\\u0CB3 \\u0CB9\\u0CBF\\u0C82\\u0CA6\\u0CC6\"\n    }\n  },\n  xMonths: {\n    one: {\n      default: \"1 \\u0CA4\\u0CBF\\u0C82\\u0C97\\u0CB3\\u0CC1\",\n      future: \"1 \\u0CA4\\u0CBF\\u0C82\\u0C97\\u0CB3\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      past: \"1 \\u0CA4\\u0CBF\\u0C82\\u0C97\\u0CB3 \\u0CB9\\u0CBF\\u0C82\\u0CA6\\u0CC6\"\n    },\n    other: {\n      default: \"{{count}} \\u0CA4\\u0CBF\\u0C82\\u0C97\\u0CB3\\u0CC1\",\n      future: \"{{count}} \\u0CA4\\u0CBF\\u0C82\\u0C97\\u0CB3\\u0CC1\\u0C97\\u0CB3\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      past: \"{{count}} \\u0CA4\\u0CBF\\u0C82\\u0C97\\u0CB3\\u0CC1\\u0C97\\u0CB3 \\u0CB9\\u0CBF\\u0C82\\u0CA6\\u0CC6\"\n    }\n  },\n  aboutXYears: {\n    one: {\n      default: \"\\u0CB8\\u0CC1\\u0CAE\\u0CBE\\u0CB0\\u0CC1 1 \\u0CB5\\u0CB0\\u0CCD\\u0CB7\",\n      future: \"\\u0CB8\\u0CC1\\u0CAE\\u0CBE\\u0CB0\\u0CC1 1 \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0CA6\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      past: \"\\u0CB8\\u0CC1\\u0CAE\\u0CBE\\u0CB0\\u0CC1 1 \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0CA6 \\u0CB9\\u0CBF\\u0C82\\u0CA6\\u0CC6\"\n    },\n    other: {\n      default: \"\\u0CB8\\u0CC1\\u0CAE\\u0CBE\\u0CB0\\u0CC1 {{count}} \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0C97\\u0CB3\\u0CC1\",\n      future: \"\\u0CB8\\u0CC1\\u0CAE\\u0CBE\\u0CB0\\u0CC1 {{count}} \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0C97\\u0CB3\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      past: \"\\u0CB8\\u0CC1\\u0CAE\\u0CBE\\u0CB0\\u0CC1 {{count}} \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0C97\\u0CB3 \\u0CB9\\u0CBF\\u0C82\\u0CA6\\u0CC6\"\n    }\n  },\n  xYears: {\n    one: {\n      default: \"1 \\u0CB5\\u0CB0\\u0CCD\\u0CB7\",\n      future: \"1 \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0CA6\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      past: \"1 \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0CA6 \\u0CB9\\u0CBF\\u0C82\\u0CA6\\u0CC6\"\n    },\n    other: {\n      default: \"{{count}} \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0C97\\u0CB3\\u0CC1\",\n      future: \"{{count}} \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0C97\\u0CB3\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      past: \"{{count}} \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0C97\\u0CB3 \\u0CB9\\u0CBF\\u0C82\\u0CA6\\u0CC6\"\n    }\n  },\n  overXYears: {\n    one: {\n      default: \"1 \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0CA6 \\u0CAE\\u0CC7\\u0CB2\\u0CC6\",\n      future: \"1 \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0CA6 \\u0CAE\\u0CC7\\u0CB2\\u0CC6\",\n      past: \"1 \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0CA6 \\u0CAE\\u0CC7\\u0CB2\\u0CC6\"\n    },\n    other: {\n      default: \"{{count}} \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0C97\\u0CB3 \\u0CAE\\u0CC7\\u0CB2\\u0CC6\",\n      future: \"{{count}} \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0C97\\u0CB3 \\u0CAE\\u0CC7\\u0CB2\\u0CC6\",\n      past: \"{{count}} \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0C97\\u0CB3 \\u0CAE\\u0CC7\\u0CB2\\u0CC6\"\n    }\n  },\n  almostXYears: {\n    one: {\n      default: \"\\u0CAC\\u0CB9\\u0CC1\\u0CA4\\u0CC7\\u0C95 1 \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0CA6\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      future: \"\\u0CAC\\u0CB9\\u0CC1\\u0CA4\\u0CC7\\u0C95 1 \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0CA6\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      past: \"\\u0CAC\\u0CB9\\u0CC1\\u0CA4\\u0CC7\\u0C95 1 \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0CA6\\u0CB2\\u0CCD\\u0CB2\\u0CBF\"\n    },\n    other: {\n      default: \"\\u0CAC\\u0CB9\\u0CC1\\u0CA4\\u0CC7\\u0C95 {{count}} \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0C97\\u0CB3\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      future: \"\\u0CAC\\u0CB9\\u0CC1\\u0CA4\\u0CC7\\u0C95 {{count}} \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0C97\\u0CB3\\u0CB2\\u0CCD\\u0CB2\\u0CBF\",\n      past: \"\\u0CAC\\u0CB9\\u0CC1\\u0CA4\\u0CC7\\u0C95 {{count}} \\u0CB5\\u0CB0\\u0CCD\\u0CB7\\u0C97\\u0CB3\\u0CB2\\u0CCD\\u0CB2\\u0CBF\"\n    }\n  }\n};\nvar formatDistance = function formatDistance(token, count, options) {\n  var result;\n  var tokenValue = formatDistanceLocale[token];\n  if (tokenValue.one && count === 1) {\n    result = getResultByTense(tokenValue.one, options);\n  } else {\n    result = getResultByTense(tokenValue.other, options);\n  }\n  return result.replace(\"{{count}}\", String(count));\n};\n\n// lib/locale/_lib/buildFormatLongFn.js\nfunction buildFormatLongFn(args) {\n  return function () {var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    var width = options.width ? String(options.width) : args.defaultWidth;\n    var format = args.formats[width] || args.formats[args.defaultWidth];\n    return format;\n  };\n}\n\n// lib/locale/kn/_lib/formatLong.js\nvar dateFormats = {\n  full: \"EEEE, MMMM d, y\",\n  long: \"MMMM d, y\",\n  medium: \"MMM d, y\",\n  short: \"d/M/yy\"\n};\nvar timeFormats = {\n  full: \"hh:mm:ss a zzzz\",\n  long: \"hh:mm:ss a z\",\n  medium: \"hh:mm:ss a\",\n  short: \"hh:mm a\"\n};\nvar dateTimeFormats = {\n  full: \"{{date}} {{time}}\",\n  long: \"{{date}} {{time}}\",\n  medium: \"{{date}} {{time}}\",\n  short: \"{{date}} {{time}}\"\n};\nvar formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\"\n  }),\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\"\n  }),\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\"\n  })\n};\n\n// lib/locale/kn/_lib/formatRelative.js\nvar formatRelativeLocale = {\n  lastWeek: \"'\\u0C95\\u0CB3\\u0CC6\\u0CA6' eeee p '\\u0C95\\u0CCD\\u0C95\\u0CC6'\",\n  yesterday: \"'\\u0CA8\\u0CBF\\u0CA8\\u0CCD\\u0CA8\\u0CC6' p '\\u0C95\\u0CCD\\u0C95\\u0CC6'\",\n  today: \"'\\u0C87\\u0C82\\u0CA6\\u0CC1' p '\\u0C95\\u0CCD\\u0C95\\u0CC6'\",\n  tomorrow: \"'\\u0CA8\\u0CBE\\u0CB3\\u0CC6' p '\\u0C95\\u0CCD\\u0C95\\u0CC6'\",\n  nextWeek: \"eeee p '\\u0C95\\u0CCD\\u0C95\\u0CC6'\",\n  other: \"P\"\n};\nvar formatRelative = function formatRelative(token, _date, _baseDate, _options) {return formatRelativeLocale[token];};\n\n// lib/locale/_lib/buildLocalizeFn.js\nfunction buildLocalizeFn(args) {\n  return function (value, options) {\n    var context = options !== null && options !== void 0 && options.context ? String(options.context) : \"standalone\";\n    var valuesArray;\n    if (context === \"formatting\" && args.formattingValues) {\n      var defaultWidth = args.defaultFormattingWidth || args.defaultWidth;\n      var width = options !== null && options !== void 0 && options.width ? String(options.width) : defaultWidth;\n      valuesArray = args.formattingValues[width] || args.formattingValues[defaultWidth];\n    } else {\n      var _defaultWidth = args.defaultWidth;\n      var _width = options !== null && options !== void 0 && options.width ? String(options.width) : args.defaultWidth;\n      valuesArray = args.values[_width] || args.values[_defaultWidth];\n    }\n    var index = args.argumentCallback ? args.argumentCallback(value) : value;\n    return valuesArray[index];\n  };\n}\n\n// lib/locale/kn/_lib/localize.js\nvar eraValues = {\n  narrow: [\"\\u0C95\\u0CCD\\u0CB0\\u0CBF.\\u0CAA\\u0CC2\", \"\\u0C95\\u0CCD\\u0CB0\\u0CBF.\\u0CB6\"],\n  abbreviated: [\"\\u0C95\\u0CCD\\u0CB0\\u0CBF.\\u0CAA\\u0CC2\", \"\\u0C95\\u0CCD\\u0CB0\\u0CBF.\\u0CB6\"],\n  wide: [\"\\u0C95\\u0CCD\\u0CB0\\u0CBF\\u0CB8\\u0CCD\\u0CA4 \\u0CAA\\u0CC2\\u0CB0\\u0CCD\\u0CB5\", \"\\u0C95\\u0CCD\\u0CB0\\u0CBF\\u0CB8\\u0CCD\\u0CA4 \\u0CB6\\u0C95\"]\n};\nvar quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"\\u0CA4\\u0CCD\\u0CB0\\u0CC8 1\", \"\\u0CA4\\u0CCD\\u0CB0\\u0CC8 2\", \"\\u0CA4\\u0CCD\\u0CB0\\u0CC8 3\", \"\\u0CA4\\u0CCD\\u0CB0\\u0CC8 4\"],\n  wide: [\"1\\u0CA8\\u0CC7 \\u0CA4\\u0CCD\\u0CB0\\u0CC8\\u0CAE\\u0CBE\\u0CB8\\u0CBF\\u0C95\", \"2\\u0CA8\\u0CC7 \\u0CA4\\u0CCD\\u0CB0\\u0CC8\\u0CAE\\u0CBE\\u0CB8\\u0CBF\\u0C95\", \"3\\u0CA8\\u0CC7 \\u0CA4\\u0CCD\\u0CB0\\u0CC8\\u0CAE\\u0CBE\\u0CB8\\u0CBF\\u0C95\", \"4\\u0CA8\\u0CC7 \\u0CA4\\u0CCD\\u0CB0\\u0CC8\\u0CAE\\u0CBE\\u0CB8\\u0CBF\\u0C95\"]\n};\nvar monthValues = {\n  narrow: [\"\\u0C9C\", \"\\u0CAB\\u0CC6\", \"\\u0CAE\\u0CBE\", \"\\u0C8F\", \"\\u0CAE\\u0CC7\", \"\\u0C9C\\u0CC2\", \"\\u0C9C\\u0CC1\", \"\\u0C86\", \"\\u0CB8\\u0CC6\", \"\\u0C85\", \"\\u0CA8\", \"\\u0CA1\\u0CBF\"],\n  abbreviated: [\n  \"\\u0C9C\\u0CA8\",\n  \"\\u0CAB\\u0CC6\\u0CAC\\u0CCD\\u0CB0\",\n  \"\\u0CAE\\u0CBE\\u0CB0\\u0CCD\\u0C9A\\u0CCD\",\n  \"\\u0C8F\\u0CAA\\u0CCD\\u0CB0\\u0CBF\",\n  \"\\u0CAE\\u0CC7\",\n  \"\\u0C9C\\u0CC2\\u0CA8\\u0CCD\",\n  \"\\u0C9C\\u0CC1\\u0CB2\\u0CC8\",\n  \"\\u0C86\\u0C97\",\n  \"\\u0CB8\\u0CC6\\u0CAA\\u0CCD\\u0C9F\\u0CC6\\u0C82\",\n  \"\\u0C85\\u0C95\\u0CCD\\u0C9F\\u0CCB\",\n  \"\\u0CA8\\u0CB5\\u0CC6\\u0C82\",\n  \"\\u0CA1\\u0CBF\\u0CB8\\u0CC6\\u0C82\"],\n\n  wide: [\n  \"\\u0C9C\\u0CA8\\u0CB5\\u0CB0\\u0CBF\",\n  \"\\u0CAB\\u0CC6\\u0CAC\\u0CCD\\u0CB0\\u0CB5\\u0CB0\\u0CBF\",\n  \"\\u0CAE\\u0CBE\\u0CB0\\u0CCD\\u0C9A\\u0CCD\",\n  \"\\u0C8F\\u0CAA\\u0CCD\\u0CB0\\u0CBF\\u0CB2\\u0CCD\",\n  \"\\u0CAE\\u0CC7\",\n  \"\\u0C9C\\u0CC2\\u0CA8\\u0CCD\",\n  \"\\u0C9C\\u0CC1\\u0CB2\\u0CC8\",\n  \"\\u0C86\\u0C97\\u0CB8\\u0CCD\\u0C9F\\u0CCD\",\n  \"\\u0CB8\\u0CC6\\u0CAA\\u0CCD\\u0C9F\\u0CC6\\u0C82\\u0CAC\\u0CB0\\u0CCD\",\n  \"\\u0C85\\u0C95\\u0CCD\\u0C9F\\u0CCB\\u0CAC\\u0CB0\\u0CCD\",\n  \"\\u0CA8\\u0CB5\\u0CC6\\u0C82\\u0CAC\\u0CB0\\u0CCD\",\n  \"\\u0CA1\\u0CBF\\u0CB8\\u0CC6\\u0C82\\u0CAC\\u0CB0\\u0CCD\"]\n\n};\nvar dayValues = {\n  narrow: [\"\\u0CAD\\u0CBE\", \"\\u0CB8\\u0CCB\", \"\\u0CAE\\u0C82\", \"\\u0CAC\\u0CC1\", \"\\u0C97\\u0CC1\", \"\\u0CB6\\u0CC1\", \"\\u0CB6\"],\n  short: [\"\\u0CAD\\u0CBE\\u0CA8\\u0CC1\", \"\\u0CB8\\u0CCB\\u0CAE\", \"\\u0CAE\\u0C82\\u0C97\\u0CB3\", \"\\u0CAC\\u0CC1\\u0CA7\", \"\\u0C97\\u0CC1\\u0CB0\\u0CC1\", \"\\u0CB6\\u0CC1\\u0C95\\u0CCD\\u0CB0\", \"\\u0CB6\\u0CA8\\u0CBF\"],\n  abbreviated: [\"\\u0CAD\\u0CBE\\u0CA8\\u0CC1\", \"\\u0CB8\\u0CCB\\u0CAE\", \"\\u0CAE\\u0C82\\u0C97\\u0CB3\", \"\\u0CAC\\u0CC1\\u0CA7\", \"\\u0C97\\u0CC1\\u0CB0\\u0CC1\", \"\\u0CB6\\u0CC1\\u0C95\\u0CCD\\u0CB0\", \"\\u0CB6\\u0CA8\\u0CBF\"],\n  wide: [\n  \"\\u0CAD\\u0CBE\\u0CA8\\u0CC1\\u0CB5\\u0CBE\\u0CB0\",\n  \"\\u0CB8\\u0CCB\\u0CAE\\u0CB5\\u0CBE\\u0CB0\",\n  \"\\u0CAE\\u0C82\\u0C97\\u0CB3\\u0CB5\\u0CBE\\u0CB0\",\n  \"\\u0CAC\\u0CC1\\u0CA7\\u0CB5\\u0CBE\\u0CB0\",\n  \"\\u0C97\\u0CC1\\u0CB0\\u0CC1\\u0CB5\\u0CBE\\u0CB0\",\n  \"\\u0CB6\\u0CC1\\u0C95\\u0CCD\\u0CB0\\u0CB5\\u0CBE\\u0CB0\",\n  \"\\u0CB6\\u0CA8\\u0CBF\\u0CB5\\u0CBE\\u0CB0\"]\n\n};\nvar dayPeriodValues = {\n  narrow: {\n    am: \"\\u0CAA\\u0CC2\\u0CB0\\u0CCD\\u0CB5\\u0CBE\\u0CB9\\u0CCD\\u0CA8\",\n    pm: \"\\u0C85\\u0CAA\\u0CB0\\u0CBE\\u0CB9\\u0CCD\\u0CA8\",\n    midnight: \"\\u0CAE\\u0CA7\\u0CCD\\u0CAF\\u0CB0\\u0CBE\\u0CA4\\u0CCD\\u0CB0\\u0CBF\",\n    noon: \"\\u0CAE\\u0CA7\\u0CCD\\u0CAF\\u0CBE\\u0CB9\\u0CCD\\u0CA8\",\n    morning: \"\\u0CAC\\u0CC6\\u0CB3\\u0C97\\u0CCD\\u0C97\\u0CC6\",\n    afternoon: \"\\u0CAE\\u0CA7\\u0CCD\\u0CAF\\u0CBE\\u0CB9\\u0CCD\\u0CA8\",\n    evening: \"\\u0CB8\\u0C82\\u0C9C\\u0CC6\",\n    night: \"\\u0CB0\\u0CBE\\u0CA4\\u0CCD\\u0CB0\\u0CBF\"\n  },\n  abbreviated: {\n    am: \"\\u0CAA\\u0CC2\\u0CB0\\u0CCD\\u0CB5\\u0CBE\\u0CB9\\u0CCD\\u0CA8\",\n    pm: \"\\u0C85\\u0CAA\\u0CB0\\u0CBE\\u0CB9\\u0CCD\\u0CA8\",\n    midnight: \"\\u0CAE\\u0CA7\\u0CCD\\u0CAF\\u0CB0\\u0CBE\\u0CA4\\u0CCD\\u0CB0\\u0CBF\",\n    noon: \"\\u0CAE\\u0CA7\\u0CCD\\u0CAF\\u0CBE\\u0CA8\\u0CCD\\u0CB9\",\n    morning: \"\\u0CAC\\u0CC6\\u0CB3\\u0C97\\u0CCD\\u0C97\\u0CC6\",\n    afternoon: \"\\u0CAE\\u0CA7\\u0CCD\\u0CAF\\u0CBE\\u0CA8\\u0CCD\\u0CB9\",\n    evening: \"\\u0CB8\\u0C82\\u0C9C\\u0CC6\",\n    night: \"\\u0CB0\\u0CBE\\u0CA4\\u0CCD\\u0CB0\\u0CBF\"\n  },\n  wide: {\n    am: \"\\u0CAA\\u0CC2\\u0CB0\\u0CCD\\u0CB5\\u0CBE\\u0CB9\\u0CCD\\u0CA8\",\n    pm: \"\\u0C85\\u0CAA\\u0CB0\\u0CBE\\u0CB9\\u0CCD\\u0CA8\",\n    midnight: \"\\u0CAE\\u0CA7\\u0CCD\\u0CAF\\u0CB0\\u0CBE\\u0CA4\\u0CCD\\u0CB0\\u0CBF\",\n    noon: \"\\u0CAE\\u0CA7\\u0CCD\\u0CAF\\u0CBE\\u0CA8\\u0CCD\\u0CB9\",\n    morning: \"\\u0CAC\\u0CC6\\u0CB3\\u0C97\\u0CCD\\u0C97\\u0CC6\",\n    afternoon: \"\\u0CAE\\u0CA7\\u0CCD\\u0CAF\\u0CBE\\u0CA8\\u0CCD\\u0CB9\",\n    evening: \"\\u0CB8\\u0C82\\u0C9C\\u0CC6\",\n    night: \"\\u0CB0\\u0CBE\\u0CA4\\u0CCD\\u0CB0\\u0CBF\"\n  }\n};\nvar formattingDayPeriodValues = {\n  narrow: {\n    am: \"\\u0CAA\\u0CC2\",\n    pm: \"\\u0C85\",\n    midnight: \"\\u0CAE\\u0CA7\\u0CCD\\u0CAF\\u0CB0\\u0CBE\\u0CA4\\u0CCD\\u0CB0\\u0CBF\",\n    noon: \"\\u0CAE\\u0CA7\\u0CCD\\u0CAF\\u0CBE\\u0CA8\\u0CCD\\u0CB9\",\n    morning: \"\\u0CAC\\u0CC6\\u0CB3\\u0C97\\u0CCD\\u0C97\\u0CC6\",\n    afternoon: \"\\u0CAE\\u0CA7\\u0CCD\\u0CAF\\u0CBE\\u0CA8\\u0CCD\\u0CB9\",\n    evening: \"\\u0CB8\\u0C82\\u0C9C\\u0CC6\",\n    night: \"\\u0CB0\\u0CBE\\u0CA4\\u0CCD\\u0CB0\\u0CBF\"\n  },\n  abbreviated: {\n    am: \"\\u0CAA\\u0CC2\\u0CB0\\u0CCD\\u0CB5\\u0CBE\\u0CB9\\u0CCD\\u0CA8\",\n    pm: \"\\u0C85\\u0CAA\\u0CB0\\u0CBE\\u0CB9\\u0CCD\\u0CA8\",\n    midnight: \"\\u0CAE\\u0CA7\\u0CCD\\u0CAF \\u0CB0\\u0CBE\\u0CA4\\u0CCD\\u0CB0\\u0CBF\",\n    noon: \"\\u0CAE\\u0CA7\\u0CCD\\u0CAF\\u0CBE\\u0CA8\\u0CCD\\u0CB9\",\n    morning: \"\\u0CAC\\u0CC6\\u0CB3\\u0C97\\u0CCD\\u0C97\\u0CC6\",\n    afternoon: \"\\u0CAE\\u0CA7\\u0CCD\\u0CAF\\u0CBE\\u0CA8\\u0CCD\\u0CB9\",\n    evening: \"\\u0CB8\\u0C82\\u0C9C\\u0CC6\",\n    night: \"\\u0CB0\\u0CBE\\u0CA4\\u0CCD\\u0CB0\\u0CBF\"\n  },\n  wide: {\n    am: \"\\u0CAA\\u0CC2\\u0CB0\\u0CCD\\u0CB5\\u0CBE\\u0CB9\\u0CCD\\u0CA8\",\n    pm: \"\\u0C85\\u0CAA\\u0CB0\\u0CBE\\u0CB9\\u0CCD\\u0CA8\",\n    midnight: \"\\u0CAE\\u0CA7\\u0CCD\\u0CAF \\u0CB0\\u0CBE\\u0CA4\\u0CCD\\u0CB0\\u0CBF\",\n    noon: \"\\u0CAE\\u0CA7\\u0CCD\\u0CAF\\u0CBE\\u0CA8\\u0CCD\\u0CB9\",\n    morning: \"\\u0CAC\\u0CC6\\u0CB3\\u0C97\\u0CCD\\u0C97\\u0CC6\",\n    afternoon: \"\\u0CAE\\u0CA7\\u0CCD\\u0CAF\\u0CBE\\u0CA8\\u0CCD\\u0CB9\",\n    evening: \"\\u0CB8\\u0C82\\u0C9C\\u0CC6\",\n    night: \"\\u0CB0\\u0CBE\\u0CA4\\u0CCD\\u0CB0\\u0CBF\"\n  }\n};\nvar ordinalNumber = function ordinalNumber(dirtyNumber, _options) {\n  var number = Number(dirtyNumber);\n  return number + \"\\u0CA8\\u0CC7\";\n};\nvar localize = {\n  ordinalNumber: ordinalNumber,\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\"\n  }),\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: function argumentCallback(quarter) {return quarter - 1;}\n  }),\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\"\n  }),\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\"\n  }),\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\"\n  })\n};\n\n// lib/locale/_lib/buildMatchFn.js\nfunction buildMatchFn(args) {\n  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var width = options.width;\n    var matchPattern = width && args.matchPatterns[width] || args.matchPatterns[args.defaultMatchWidth];\n    var matchResult = string.match(matchPattern);\n    if (!matchResult) {\n      return null;\n    }\n    var matchedString = matchResult[0];\n    var parsePatterns = width && args.parsePatterns[width] || args.parsePatterns[args.defaultParseWidth];\n    var key = Array.isArray(parsePatterns) ? findIndex(parsePatterns, function (pattern) {return pattern.test(matchedString);}) : findKey(parsePatterns, function (pattern) {return pattern.test(matchedString);});\n    var value;\n    value = args.valueCallback ? args.valueCallback(key) : key;\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return { value: value, rest: rest };\n  };\n}\nfunction findKey(object, predicate) {\n  for (var key in object) {\n    if (Object.prototype.hasOwnProperty.call(object, key) && predicate(object[key])) {\n      return key;\n    }\n  }\n  return;\n}\nfunction findIndex(array, predicate) {\n  for (var key = 0; key < array.length; key++) {\n    if (predicate(array[key])) {\n      return key;\n    }\n  }\n  return;\n}\n\n// lib/locale/_lib/buildMatchPatternFn.js\nfunction buildMatchPatternFn(args) {\n  return function (string) {var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    var matchResult = string.match(args.matchPattern);\n    if (!matchResult)\n    return null;\n    var matchedString = matchResult[0];\n    var parseResult = string.match(args.parsePattern);\n    if (!parseResult)\n    return null;\n    var value = args.valueCallback ? args.valueCallback(parseResult[0]) : parseResult[0];\n    value = options.valueCallback ? options.valueCallback(value) : value;\n    var rest = string.slice(matchedString.length);\n    return { value: value, rest: rest };\n  };\n}\n\n// lib/locale/kn/_lib/match.js\nvar matchOrdinalNumberPattern = /^(\\d+)(ನೇ|ನೆ)?/i;\nvar parseOrdinalNumberPattern = /\\d+/i;\nvar matchEraPatterns = {\n  narrow: /^(ಕ್ರಿ.ಪೂ|ಕ್ರಿ.ಶ)/i,\n  abbreviated: /^(ಕ್ರಿ\\.?\\s?ಪೂ\\.?|ಕ್ರಿ\\.?\\s?ಶ\\.?|ಪ್ರ\\.?\\s?ಶ\\.?)/i,\n  wide: /^(ಕ್ರಿಸ್ತ ಪೂರ್ವ|ಕ್ರಿಸ್ತ ಶಕ|ಪ್ರಸಕ್ತ ಶಕ)/i\n};\nvar parseEraPatterns = {\n  any: [/^ಪೂ/i, /^(ಶ|ಪ್ರ)/i]\n};\nvar matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^ತ್ರೈ[1234]|ತ್ರೈ [1234]| [1234]ತ್ರೈ/i,\n  wide: /^[1234](ನೇ)? ತ್ರೈಮಾಸಿಕ/i\n};\nvar parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i]\n};\nvar matchMonthPatterns = {\n  narrow: /^(ಜೂ|ಜು|ಜ|ಫೆ|ಮಾ|ಏ|ಮೇ|ಆ|ಸೆ|ಅ|ನ|ಡಿ)/i,\n  abbreviated: /^(ಜನ|ಫೆಬ್ರ|ಮಾರ್ಚ್|ಏಪ್ರಿ|ಮೇ|ಜೂನ್|ಜುಲೈ|ಆಗ|ಸೆಪ್ಟೆಂ|ಅಕ್ಟೋ|ನವೆಂ|ಡಿಸೆಂ)/i,\n  wide: /^(ಜನವರಿ|ಫೆಬ್ರವರಿ|ಮಾರ್ಚ್|ಏಪ್ರಿಲ್|ಮೇ|ಜೂನ್|ಜುಲೈ|ಆಗಸ್ಟ್|ಸೆಪ್ಟೆಂಬರ್|ಅಕ್ಟೋಬರ್|ನವೆಂಬರ್|ಡಿಸೆಂಬರ್)/i\n};\nvar parseMonthPatterns = {\n  narrow: [\n  /^ಜ$/i,\n  /^ಫೆ/i,\n  /^ಮಾ/i,\n  /^ಏ/i,\n  /^ಮೇ/i,\n  /^ಜೂ/i,\n  /^ಜು$/i,\n  /^ಆ/i,\n  /^ಸೆ/i,\n  /^ಅ/i,\n  /^ನ/i,\n  /^ಡಿ/i],\n\n  any: [\n  /^ಜನ/i,\n  /^ಫೆ/i,\n  /^ಮಾ/i,\n  /^ಏ/i,\n  /^ಮೇ/i,\n  /^ಜೂನ್/i,\n  /^ಜುಲೈ/i,\n  /^ಆ/i,\n  /^ಸೆ/i,\n  /^ಅ/i,\n  /^ನ/i,\n  /^ಡಿ/i]\n\n};\nvar matchDayPatterns = {\n  narrow: /^(ಭಾ|ಸೋ|ಮ|ಬು|ಗು|ಶು|ಶ)/i,\n  short: /^(ಭಾನು|ಸೋಮ|ಮಂಗಳ|ಬುಧ|ಗುರು|ಶುಕ್ರ|ಶನಿ)/i,\n  abbreviated: /^(ಭಾನು|ಸೋಮ|ಮಂಗಳ|ಬುಧ|ಗುರು|ಶುಕ್ರ|ಶನಿ)/i,\n  wide: /^(ಭಾನುವಾರ|ಸೋಮವಾರ|ಮಂಗಳವಾರ|ಬುಧವಾರ|ಗುರುವಾರ|ಶುಕ್ರವಾರ|ಶನಿವಾರ)/i\n};\nvar parseDayPatterns = {\n  narrow: [/^ಭಾ/i, /^ಸೋ/i, /^ಮ/i, /^ಬು/i, /^ಗು/i, /^ಶು/i, /^ಶ/i],\n  any: [/^ಭಾ/i, /^ಸೋ/i, /^ಮ/i, /^ಬು/i, /^ಗು/i, /^ಶು/i, /^ಶ/i]\n};\nvar matchDayPeriodPatterns = {\n  narrow: /^(ಪೂ|ಅ|ಮಧ್ಯರಾತ್ರಿ|ಮಧ್ಯಾನ್ಹ|ಬೆಳಗ್ಗೆ|ಸಂಜೆ|ರಾತ್ರಿ)/i,\n  any: /^(ಪೂರ್ವಾಹ್ನ|ಅಪರಾಹ್ನ|ಮಧ್ಯರಾತ್ರಿ|ಮಧ್ಯಾನ್ಹ|ಬೆಳಗ್ಗೆ|ಸಂಜೆ|ರಾತ್ರಿ)/i\n};\nvar parseDayPeriodPatterns = {\n  any: {\n    am: /^ಪೂ/i,\n    pm: /^ಅ/i,\n    midnight: /ಮಧ್ಯರಾತ್ರಿ/i,\n    noon: /ಮಧ್ಯಾನ್ಹ/i,\n    morning: /ಬೆಳಗ್ಗೆ/i,\n    afternoon: /ಮಧ್ಯಾನ್ಹ/i,\n    evening: /ಸಂಜೆ/i,\n    night: /ರಾತ್ರಿ/i\n  }\n};\nvar match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: function valueCallback(value) {return parseInt(value, 10);}\n  }),\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: function valueCallback(index) {return index + 1;}\n  }),\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\"\n  }),\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\"\n  })\n};\n\n// lib/locale/kn.js\nvar kn = {\n  code: \"kn\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1,\n    firstWeekContainsDate: 1\n  }\n};\n\n// lib/locale/kn/cdn.js\nwindow.dateFns = _objectSpread(_objectSpread({},\nwindow.dateFns), {}, {\n  locale: _objectSpread(_objectSpread({}, (_window$dateFns =\n  window.dateFns) === null || _window$dateFns === void 0 ? void 0 : _window$dateFns.locale), {}, {\n    kn: kn }) });\n\n\n\n//# debugId=95791F86686E063964756E2164756E21\n\n//# sourceMappingURL=cdn.js.map\n})();"], "mappings": "AAAA,CAAC,IAAM,CACP,IAAI,EAAgB,SAAS,CAAO,CAAC,EAAG,CAA2B,OAAO,SAA+B,QAArB,mBAAkD,OAAO,UAA1B,iBAA8C,CAAC,EAAG,CAAC,cAAc,WAAe,CAAC,EAAG,CAAC,OAAO,UAA0B,QAArB,YAA+B,EAAE,cAAgB,QAAU,IAAM,OAAO,UAAY,gBAAkB,GAAK,EAAQ,CAAC,EAAG,SAAS,CAAO,CAAC,EAAG,EAAG,CAAC,IAAI,EAAI,OAAO,KAAK,CAAC,EAAE,GAAI,OAAO,sBAAuB,CAAC,IAAI,EAAI,OAAO,sBAAsB,CAAC,EAAE,IAAM,EAAI,EAAE,eAAgB,CAAC,EAAG,CAAC,OAAO,OAAO,yBAAyB,EAAG,CAAC,EAAE,WAAY,GAAI,EAAE,KAAK,MAAM,EAAG,CAAC,EAAG,OAAO,EAAG,SAAS,CAAa,CAAC,EAAG,CAAC,QAAS,EAAI,EAAG,EAAI,UAAU,OAAQ,IAAK,CAAC,IAAI,EAAY,UAAU,IAAlB,KAAuB,UAAU,GAAK,CAAC,EAAE,EAAI,EAAI,EAAQ,OAAO,CAAC,EAAG,EAAE,EAAE,gBAAiB,CAAC,EAAG,CAAC,EAAgB,EAAG,EAAG,EAAE,EAAE,EAAG,EAAI,OAAO,0BAA4B,OAAO,iBAAiB,EAAG,OAAO,0BAA0B,CAAC,CAAC,EAAI,EAAQ,OAAO,CAAC,CAAC,EAAE,gBAAiB,CAAC,EAAG,CAAC,OAAO,eAAe,EAAG,EAAG,OAAO,yBAAyB,EAAG,CAAC,CAAC,EAAG,EAAG,OAAO,EAAG,SAAS,CAAe,CAAC,EAAK,EAAK,EAAO,CAA2B,GAA1B,EAAM,EAAe,CAAG,EAAM,KAAO,EAAM,OAAO,eAAe,EAAK,EAAK,CAAE,MAAO,EAAO,WAAY,GAAM,aAAc,GAAM,SAAU,EAAK,CAAC,MAAU,GAAI,GAAO,EAAO,OAAO,EAAK,SAAS,CAAc,CAAC,EAAG,CAAC,IAAI,EAAI,EAAa,EAAG,QAAQ,EAAE,OAAmB,EAAQ,CAAC,GAArB,SAAyB,EAAI,OAAO,CAAC,EAAG,SAAS,CAAY,CAAC,EAAG,EAAG,CAAC,GAAgB,EAAQ,CAAC,GAArB,WAA2B,EAAG,OAAO,EAAE,IAAI,EAAI,EAAE,OAAO,aAAa,GAAe,IAAN,OAAS,CAAC,IAAI,EAAI,EAAE,KAAK,EAAG,GAAK,SAAS,EAAE,GAAgB,EAAQ,CAAC,GAArB,SAAwB,OAAO,EAAE,MAAM,IAAI,UAAU,8CAA8C,EAAG,OAAqB,IAAb,SAAiB,OAAS,QAAQ,CAAC,EAAG,IAAI,EAAY,OAAO,eACroD,YAAoB,CAAQ,CAAC,EAAQ,EAAK,CAC5C,QAAS,KAAQ,EACjB,EAAU,EAAQ,EAAM,CACtB,IAAK,EAAI,GACT,WAAY,GACZ,aAAc,GACd,aAAc,CAAG,CAAC,EAAU,CAAC,OAAO,EAAI,WAAiB,EAAG,CAAC,OAAO,GACtE,CAAC,GAIH,SAAS,CAAgB,CAAC,EAAa,EAAS,CAC9C,GAAI,IAAY,MAAQ,IAAiB,QAAK,EAAQ,UACpD,GAAI,EAAQ,YAAc,EAAQ,WAAa,EAC7C,OAAO,EAAY,WAEnB,QAAO,EAAY,KAGvB,OAAO,EAAY,QAErB,IAAI,EAAuB,CACzB,iBAAkB,CAChB,IAAK,CACH,QAAS,4GACT,OAAQ,4GACR,KAAM,2GACR,EACA,MAAO,CACL,QAAS,oHACT,OAAQ,oHACR,KAAM,mHACR,CACF,EACA,SAAU,CACR,IAAK,CACH,QAAS,+CACT,OAAQ,mFACR,KAAM,6EACR,EACA,MAAO,CACL,QAAS,yEACT,OAAQ,iGACR,KAAM,qFACR,CACF,EACA,YAAa,CACX,MAAO,CACL,QAAS,0DACT,OAAQ,wFACR,KAAM,8FACR,CACF,EACA,iBAAkB,CAChB,IAAK,CACH,QAAS,sGACT,OAAQ,sGACR,KAAM,qGACR,EACA,MAAO,CACL,QAAS,8GACT,OAAQ,8GACR,KAAM,6GACR,CACF,EACA,SAAU,CACR,IAAK,CACH,QAAS,mCACT,OAAQ,iEACR,KAAM,uEACR,EACA,MAAO,CACL,QAAS,6DACT,OAAQ,+EACR,KAAM,qFACR,CACF,EACA,YAAa,CACX,IAAK,CACH,QAAS,kEACT,OAAQ,gGACR,KAAM,gGACR,EACA,MAAO,CACL,QAAS,4FACT,OAAQ,8GACR,KAAM,oHACR,CACF,EACA,OAAQ,CACN,IAAK,CACH,QAAS,6BACT,OAAQ,2DACR,KAAM,2DACR,EACA,MAAO,CACL,QAAS,uDACT,OAAQ,yEACR,KAAM,+EACR,CACF,EACA,MAAO,CACL,IAAK,CACH,QAAS,uBACT,OAAQ,qDACR,KAAM,2DACR,EACA,MAAO,CACL,QAAS,iDACT,OAAQ,mEACR,KAAM,yEACR,CACF,EACA,aAAc,CACZ,IAAK,CACH,QAAS,8EACT,OAAQ,gGACR,KAAM,sGACR,EACA,MAAO,CACL,QAAS,sFACT,OAAQ,0HACR,KAAM,gIACR,CACF,EACA,QAAS,CACP,IAAK,CACH,QAAS,yCACT,OAAQ,2DACR,KAAM,iEACR,EACA,MAAO,CACL,QAAS,iDACT,OAAQ,qFACR,KAAM,2FACR,CACF,EACA,YAAa,CACX,IAAK,CACH,QAAS,kEACT,OAAQ,gGACR,KAAM,sGACR,EACA,MAAO,CACL,QAAS,4FACT,OAAQ,8GACR,KAAM,oHACR,CACF,EACA,OAAQ,CACN,IAAK,CACH,QAAS,6BACT,OAAQ,2DACR,KAAM,iEACR,EACA,MAAO,CACL,QAAS,uDACT,OAAQ,yEACR,KAAM,+EACR,CACF,EACA,WAAY,CACV,IAAK,CACH,QAAS,4DACT,OAAQ,4DACR,KAAM,2DACR,EACA,MAAO,CACL,QAAS,0EACT,OAAQ,0EACR,KAAM,yEACR,CACF,EACA,aAAc,CACZ,IAAK,CACH,QAAS,gGACT,OAAQ,gGACR,KAAM,+FACR,EACA,MAAO,CACL,QAAS,8GACT,OAAQ,8GACR,KAAM,6GACR,CACF,CACF,EACI,WAA0B,CAAc,CAAC,EAAO,EAAO,EAAS,CAClE,IAAI,EACA,EAAa,EAAqB,GACtC,GAAI,EAAW,KAAO,IAAU,EAC9B,EAAS,EAAiB,EAAW,IAAK,CAAO,MAEjD,GAAS,EAAiB,EAAW,MAAO,CAAO,EAErD,OAAO,EAAO,QAAQ,YAAa,OAAO,CAAK,CAAC,GAIlD,SAAS,CAAiB,CAAC,EAAM,CAC/B,eAAgB,EAAG,CAAC,IAAI,EAAU,UAAU,OAAS,GAAK,UAAU,KAAO,OAAY,UAAU,GAAK,CAAC,EACjG,EAAQ,EAAQ,MAAQ,OAAO,EAAQ,KAAK,EAAI,EAAK,aACrD,EAAS,EAAK,QAAQ,IAAU,EAAK,QAAQ,EAAK,cACtD,OAAO,GAKX,IAAI,EAAc,CAChB,KAAM,kBACN,KAAM,YACN,OAAQ,WACR,MAAO,QACT,EACI,EAAc,CAChB,KAAM,kBACN,KAAM,eACN,OAAQ,aACR,MAAO,SACT,EACI,EAAkB,CACpB,KAAM,oBACN,KAAM,oBACN,OAAQ,oBACR,MAAO,mBACT,EACI,EAAa,CACf,KAAM,EAAkB,CACtB,QAAS,EACT,aAAc,MAChB,CAAC,EACD,KAAM,EAAkB,CACtB,QAAS,EACT,aAAc,MAChB,CAAC,EACD,SAAU,EAAkB,CAC1B,QAAS,EACT,aAAc,MAChB,CAAC,CACH,EAGI,EAAuB,CACzB,SAAU,+DACV,UAAW,sEACX,MAAO,0DACP,SAAU,0DACV,SAAU,oCACV,MAAO,GACT,EACI,WAA0B,CAAc,CAAC,EAAO,EAAO,EAAW,EAAU,CAAC,OAAO,EAAqB,IAG7G,SAAS,CAAe,CAAC,EAAM,CAC7B,eAAgB,CAAC,EAAO,EAAS,CAC/B,IAAI,EAAU,IAAY,MAAQ,IAAiB,QAAK,EAAQ,QAAU,OAAO,EAAQ,OAAO,EAAI,aAChG,EACJ,GAAI,IAAY,cAAgB,EAAK,iBAAkB,CACrD,IAAI,EAAe,EAAK,wBAA0B,EAAK,aACnD,EAAQ,IAAY,MAAQ,IAAiB,QAAK,EAAQ,MAAQ,OAAO,EAAQ,KAAK,EAAI,EAC9F,EAAc,EAAK,iBAAiB,IAAU,EAAK,iBAAiB,OAC/D,CACL,IAAI,EAAgB,EAAK,aACrB,EAAS,IAAY,MAAQ,IAAiB,QAAK,EAAQ,MAAQ,OAAO,EAAQ,KAAK,EAAI,EAAK,aACpG,EAAc,EAAK,OAAO,IAAW,EAAK,OAAO,GAEnD,IAAI,EAAQ,EAAK,iBAAmB,EAAK,iBAAiB,CAAK,EAAI,EACnE,OAAO,EAAY,IAKvB,IAAI,EAAY,CACd,OAAQ,CAAC,wCAAyC,iCAAiC,EACnF,YAAa,CAAC,wCAAyC,iCAAiC,EACxF,KAAM,CAAC,4EAA6E,yDAAyD,CAC/I,EACI,EAAgB,CAClB,OAAQ,CAAC,IAAK,IAAK,IAAK,GAAG,EAC3B,YAAa,CAAC,6BAA8B,6BAA8B,6BAA8B,4BAA4B,EACpI,KAAM,CAAC,uEAAwE,uEAAwE,uEAAwE,sEAAsE,CACvS,EACI,EAAc,CAChB,OAAQ,CAAC,SAAU,eAAgB,eAAgB,SAAU,eAAgB,eAAgB,eAAgB,SAAU,eAAgB,SAAU,SAAU,cAAc,EACzK,YAAa,CACb,eACA,iCACA,uCACA,iCACA,eACA,2BACA,2BACA,eACA,6CACA,iCACA,2BACA,gCAAgC,EAEhC,KAAM,CACN,iCACA,mDACA,uCACA,6CACA,eACA,2BACA,2BACA,uCACA,+DACA,mDACA,6CACA,kDAAkD,CAEpD,EACI,EAAY,CACd,OAAQ,CAAC,eAAgB,eAAgB,eAAgB,eAAgB,eAAgB,eAAgB,QAAQ,EACjH,MAAO,CAAC,2BAA4B,qBAAsB,2BAA4B,qBAAsB,2BAA4B,iCAAkC,oBAAoB,EAC9L,YAAa,CAAC,2BAA4B,qBAAsB,2BAA4B,qBAAsB,2BAA4B,iCAAkC,oBAAoB,EACpM,KAAM,CACN,6CACA,uCACA,6CACA,uCACA,6CACA,mDACA,sCAAsC,CAExC,EACI,EAAkB,CACpB,OAAQ,CACN,GAAI,yDACJ,GAAI,6CACJ,SAAU,+DACV,KAAM,mDACN,QAAS,6CACT,UAAW,mDACX,QAAS,2BACT,MAAO,sCACT,EACA,YAAa,CACX,GAAI,yDACJ,GAAI,6CACJ,SAAU,+DACV,KAAM,mDACN,QAAS,6CACT,UAAW,mDACX,QAAS,2BACT,MAAO,sCACT,EACA,KAAM,CACJ,GAAI,yDACJ,GAAI,6CACJ,SAAU,+DACV,KAAM,mDACN,QAAS,6CACT,UAAW,mDACX,QAAS,2BACT,MAAO,sCACT,CACF,EACI,EAA4B,CAC9B,OAAQ,CACN,GAAI,eACJ,GAAI,SACJ,SAAU,+DACV,KAAM,mDACN,QAAS,6CACT,UAAW,mDACX,QAAS,2BACT,MAAO,sCACT,EACA,YAAa,CACX,GAAI,yDACJ,GAAI,6CACJ,SAAU,gEACV,KAAM,mDACN,QAAS,6CACT,UAAW,mDACX,QAAS,2BACT,MAAO,sCACT,EACA,KAAM,CACJ,GAAI,yDACJ,GAAI,6CACJ,SAAU,gEACV,KAAM,mDACN,QAAS,6CACT,UAAW,mDACX,QAAS,2BACT,MAAO,sCACT,CACF,EACI,WAAyB,CAAa,CAAC,EAAa,EAAU,CAChE,IAAI,EAAS,OAAO,CAAW,EAC/B,OAAO,EAAS,gBAEd,EAAW,CACb,cAAe,EACf,IAAK,EAAgB,CACnB,OAAQ,EACR,aAAc,MAChB,CAAC,EACD,QAAS,EAAgB,CACvB,OAAQ,EACR,aAAc,OACd,0BAA2B,CAAgB,CAAC,EAAS,CAAC,OAAO,EAAU,EACzE,CAAC,EACD,MAAO,EAAgB,CACrB,OAAQ,EACR,aAAc,MAChB,CAAC,EACD,IAAK,EAAgB,CACnB,OAAQ,EACR,aAAc,MAChB,CAAC,EACD,UAAW,EAAgB,CACzB,OAAQ,EACR,aAAc,OACd,iBAAkB,EAClB,uBAAwB,MAC1B,CAAC,CACH,EAGA,SAAS,CAAY,CAAC,EAAM,CAC1B,eAAgB,CAAC,EAAQ,CAAC,IAAI,EAAU,UAAU,OAAS,GAAK,UAAU,KAAO,OAAY,UAAU,GAAK,CAAC,EACvG,EAAQ,EAAQ,MAChB,EAAe,GAAS,EAAK,cAAc,IAAU,EAAK,cAAc,EAAK,mBAC7E,EAAc,EAAO,MAAM,CAAY,EAC3C,IAAK,EACH,OAAO,KAET,IAAI,EAAgB,EAAY,GAC5B,EAAgB,GAAS,EAAK,cAAc,IAAU,EAAK,cAAc,EAAK,mBAC9E,EAAM,MAAM,QAAQ,CAAa,EAAI,EAAU,UAAwB,CAAC,EAAS,CAAC,OAAO,EAAQ,KAAK,CAAa,EAAG,EAAI,EAAQ,UAAwB,CAAC,EAAS,CAAC,OAAO,EAAQ,KAAK,CAAa,EAAG,EACzM,EACJ,EAAQ,EAAK,cAAgB,EAAK,cAAc,CAAG,EAAI,EACvD,EAAQ,EAAQ,cAAgB,EAAQ,cAAc,CAAK,EAAI,EAC/D,IAAI,GAAO,EAAO,MAAM,EAAc,MAAM,EAC5C,MAAO,CAAE,MAAO,EAAO,KAAM,EAAK,GAGtC,SAAS,CAAO,CAAC,EAAQ,EAAW,CAClC,QAAS,KAAO,EACd,GAAI,OAAO,UAAU,eAAe,KAAK,EAAQ,CAAG,GAAK,EAAU,EAAO,EAAI,EAC5E,OAAO,EAGX,OAEF,SAAS,CAAS,CAAC,EAAO,EAAW,CACnC,QAAS,EAAM,EAAG,EAAM,EAAM,OAAQ,IACpC,GAAI,EAAU,EAAM,EAAI,EACtB,OAAO,EAGX,OAIF,SAAS,CAAmB,CAAC,EAAM,CACjC,eAAgB,CAAC,EAAQ,CAAC,IAAI,EAAU,UAAU,OAAS,GAAK,UAAU,KAAO,OAAY,UAAU,GAAK,CAAC,EACvG,EAAc,EAAO,MAAM,EAAK,YAAY,EAChD,IAAK,EACL,OAAO,KACP,IAAI,EAAgB,EAAY,GAC5B,EAAc,EAAO,MAAM,EAAK,YAAY,EAChD,IAAK,EACL,OAAO,KACP,IAAI,EAAQ,EAAK,cAAgB,EAAK,cAAc,EAAY,EAAE,EAAI,EAAY,GAClF,EAAQ,EAAQ,cAAgB,EAAQ,cAAc,CAAK,EAAI,EAC/D,IAAI,EAAO,EAAO,MAAM,EAAc,MAAM,EAC5C,MAAO,CAAE,MAAO,EAAO,KAAM,CAAK,GAKtC,IAAI,EAA4B,kBAC5B,EAA4B,OAC5B,EAAmB,CACrB,OAAQ,qBACR,YAAa,mDACb,KAAM,yCACR,EACI,EAAmB,CACrB,IAAK,CAAC,OAAO,WAAW,CAC1B,EACI,EAAuB,CACzB,OAAQ,WACR,YAAa,uCACb,KAAM,yBACR,EACI,EAAuB,CACzB,IAAK,CAAC,KAAM,KAAM,KAAM,IAAI,CAC9B,EACI,EAAqB,CACvB,OAAQ,qCACR,YAAa,qEACb,KAAM,4FACR,EACI,EAAqB,CACvB,OAAQ,CACR,OACA,OACA,OACA,MACA,OACA,OACA,QACA,MACA,OACA,MACA,MACA,MAAK,EAEL,IAAK,CACL,OACA,OACA,OACA,MACA,OACA,SACA,SACA,MACA,OACA,MACA,MACA,MAAK,CAEP,EACI,EAAmB,CACrB,OAAQ,yBACR,MAAO,uCACP,YAAa,uCACb,KAAM,2DACR,EACI,EAAmB,CACrB,OAAQ,CAAC,OAAO,OAAQ,MAAO,OAAQ,OAAQ,OAAQ,KAAK,EAC5D,IAAK,CAAC,OAAO,OAAQ,MAAO,OAAQ,OAAQ,OAAQ,KAAK,CAC3D,EACI,EAAyB,CAC3B,OAAQ,mDACR,IAAK,+DACP,EACI,EAAyB,CAC3B,IAAK,CACH,GAAI,OACJ,GAAI,MACJ,SAAU,cACV,KAAM,YACN,QAAS,WACT,UAAW,YACX,QAAS,QACT,MAAO,SACT,CACF,EACI,GAAQ,CACV,cAAe,EAAoB,CACjC,aAAc,EACd,aAAc,EACd,uBAAwB,CAAa,CAAC,EAAO,CAAC,OAAO,SAAS,EAAO,EAAE,EACzE,CAAC,EACD,IAAK,EAAa,CAChB,cAAe,EACf,kBAAmB,OACnB,cAAe,EACf,kBAAmB,KACrB,CAAC,EACD,QAAS,EAAa,CACpB,cAAe,EACf,kBAAmB,OACnB,cAAe,EACf,kBAAmB,MACnB,uBAAwB,CAAa,CAAC,EAAO,CAAC,OAAO,EAAQ,EAC/D,CAAC,EACD,MAAO,EAAa,CAClB,cAAe,EACf,kBAAmB,OACnB,cAAe,EACf,kBAAmB,KACrB,CAAC,EACD,IAAK,EAAa,CAChB,cAAe,EACf,kBAAmB,OACnB,cAAe,EACf,kBAAmB,KACrB,CAAC,EACD,UAAW,EAAa,CACtB,cAAe,EACf,kBAAmB,MACnB,cAAe,EACf,kBAAmB,KACrB,CAAC,CACH,EAGI,GAAK,CACP,KAAM,KACN,eAAgB,EAChB,WAAY,EACZ,eAAgB,EAChB,SAAU,EACV,MAAO,GACP,QAAS,CACP,aAAc,EACd,sBAAuB,CACzB,CACF,EAGA,OAAO,QAAU,EAAc,EAAc,CAAC,EAC9C,OAAO,OAAO,EAAG,CAAC,EAAG,CACnB,OAAQ,EAAc,EAAc,CAAC,GAAI,EACzC,OAAO,WAAa,MAAQ,IAAyB,OAAS,OAAI,EAAgB,MAAM,EAAG,CAAC,EAAG,CAC7F,GAAI,EAAG,CAAC,CAAE,CAAC,IAOZ", "debugId": "7E22B58B2DE3195B64756E2164756E21", "names": []}