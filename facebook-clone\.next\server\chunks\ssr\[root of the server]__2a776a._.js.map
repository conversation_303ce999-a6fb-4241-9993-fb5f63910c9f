{"version": 3, "sources": [], "sections": [{"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/fbaug/facebook-clone/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database types (will be generated from Supabase)\nexport type Database = {\n  public: {\n    Tables: {\n      profiles: {\n        Row: {\n          id: string\n          email: string\n          full_name: string | null\n          avatar_url: string | null\n          bio: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          email: string\n          full_name?: string | null\n          avatar_url?: string | null\n          bio?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          email?: string\n          full_name?: string | null\n          avatar_url?: string | null\n          bio?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      posts: {\n        Row: {\n          id: string\n          user_id: string\n          content: string\n          image_url: string | null\n          likes_count: number\n          comments_count: number\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          content: string\n          image_url?: string | null\n          likes_count?: number\n          comments_count?: number\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          content?: string\n          image_url?: string | null\n          likes_count?: number\n          comments_count?: number\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      likes: {\n        Row: {\n          id: string\n          user_id: string\n          post_id: string\n          created_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          post_id: string\n          created_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          post_id?: string\n          created_at?: string\n        }\n      }\n      comments: {\n        Row: {\n          id: string\n          user_id: string\n          post_id: string\n          content: string\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          user_id: string\n          post_id: string\n          content: string\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          user_id?: string\n          post_id?: string\n          content?: string\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      friendships: {\n        Row: {\n          id: string\n          requester_id: string\n          addressee_id: string\n          status: 'pending' | 'accepted' | 'declined'\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          requester_id: string\n          addressee_id: string\n          status?: 'pending' | 'accepted' | 'declined'\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          requester_id?: string\n          addressee_id?: string\n          status?: 'pending' | 'accepted' | 'declined'\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n    Views: {\n      [_ in never]: never\n    }\n    Functions: {\n      [_ in never]: never\n    }\n    Enums: {\n      [_ in never]: never\n    }\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,cAAc,2EAAwC;AAC5D,MAAM,kBAAkB,iEAA6C;AAE9D,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa"}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/fbaug/facebook-clone/src/lib/stores/auth-store.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { User } from '@supabase/supabase-js'\nimport { supabase } from '@/lib/supabase'\n\ninterface Profile {\n  id: string\n  email: string\n  full_name: string | null\n  avatar_url: string | null\n  bio: string | null\n  created_at: string\n  updated_at: string\n}\n\ninterface AuthState {\n  user: User | null\n  profile: Profile | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<{ error: any }>\n  signUp: (email: string, password: string, fullName: string) => Promise<{ error: any }>\n  signOut: () => Promise<void>\n  updateProfile: (updates: Partial<Profile>) => Promise<{ error: any }>\n  initialize: () => Promise<void>\n}\n\nexport const useAuthStore = create<AuthState>((set, get) => ({\n  user: null,\n  profile: null,\n  loading: true,\n\n  signIn: async (email: string, password: string) => {\n    try {\n      const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password,\n      })\n\n      if (error) return { error }\n\n      if (data.user) {\n        // Fetch user profile\n        const { data: profile } = await supabase\n          .from('profiles')\n          .select('*')\n          .eq('id', data.user.id)\n          .single()\n\n        set({ user: data.user, profile })\n      }\n\n      return { error: null }\n    } catch (error) {\n      return { error }\n    }\n  },\n\n  signUp: async (email: string, password: string, fullName: string) => {\n    try {\n      const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n      })\n\n      if (error) return { error }\n\n      if (data.user) {\n        // Create user profile\n        const { error: profileError } = await supabase\n          .from('profiles')\n          .insert({\n            id: data.user.id,\n            email: data.user.email!,\n            full_name: fullName,\n          })\n\n        if (profileError) return { error: profileError }\n\n        // Fetch the created profile\n        const { data: profile } = await supabase\n          .from('profiles')\n          .select('*')\n          .eq('id', data.user.id)\n          .single()\n\n        set({ user: data.user, profile })\n      }\n\n      return { error: null }\n    } catch (error) {\n      return { error }\n    }\n  },\n\n  signOut: async () => {\n    await supabase.auth.signOut()\n    set({ user: null, profile: null })\n  },\n\n  updateProfile: async (updates: Partial<Profile>) => {\n    try {\n      const { user } = get()\n      if (!user) return { error: 'No user logged in' }\n\n      const { data, error } = await supabase\n        .from('profiles')\n        .update({ ...updates, updated_at: new Date().toISOString() })\n        .eq('id', user.id)\n        .select()\n        .single()\n\n      if (error) return { error }\n\n      set({ profile: data })\n      return { error: null }\n    } catch (error) {\n      return { error }\n    }\n  },\n\n  initialize: async () => {\n    try {\n      const { data: { session } } = await supabase.auth.getSession()\n      \n      if (session?.user) {\n        const { data: profile } = await supabase\n          .from('profiles')\n          .select('*')\n          .eq('id', session.user.id)\n          .single()\n\n        set({ user: session.user, profile })\n      }\n    } catch (error) {\n      console.error('Error initializing auth:', error)\n    } finally {\n      set({ loading: false })\n    }\n\n    // Listen for auth changes\n    supabase.auth.onAuthStateChange(async (event, session) => {\n      if (session?.user) {\n        const { data: profile } = await supabase\n          .from('profiles')\n          .select('*')\n          .eq('id', session.user.id)\n          .single()\n\n        set({ user: session.user, profile })\n      } else {\n        set({ user: null, profile: null })\n      }\n    })\n  },\n}))\n"], "names": [], "mappings": ";;;AAEA;AAFA;;;AAyBO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAa,CAAC,KAAK,MAAQ,CAAC;QAC3D,MAAM;QACN,SAAS;QACT,SAAS;QAET,QAAQ,OAAO,OAAe;YAC5B,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;oBAC7D;oBACA;gBACF;gBAEA,IAAI,OAAO,OAAO;oBAAE;gBAAM;gBAE1B,IAAI,KAAK,IAAI,EAAE;oBACb,qBAAqB;oBACrB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACrC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,EACrB,MAAM;oBAET,IAAI;wBAAE,MAAM,KAAK,IAAI;wBAAE;oBAAQ;gBACjC;gBAEA,OAAO;oBAAE,OAAO;gBAAK;YACvB,EAAE,OAAO,OAAO;gBACd,OAAO;oBAAE;gBAAM;YACjB;QACF;QAEA,QAAQ,OAAO,OAAe,UAAkB;YAC9C,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;oBACjD;oBACA;gBACF;gBAEA,IAAI,OAAO,OAAO;oBAAE;gBAAM;gBAE1B,IAAI,KAAK,IAAI,EAAE;oBACb,sBAAsB;oBACtB,MAAM,EAAE,OAAO,YAAY,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC3C,IAAI,CAAC,YACL,MAAM,CAAC;wBACN,IAAI,KAAK,IAAI,CAAC,EAAE;wBAChB,OAAO,KAAK,IAAI,CAAC,KAAK;wBACtB,WAAW;oBACb;oBAEF,IAAI,cAAc,OAAO;wBAAE,OAAO;oBAAa;oBAE/C,4BAA4B;oBAC5B,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACrC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,KAAK,IAAI,CAAC,EAAE,EACrB,MAAM;oBAET,IAAI;wBAAE,MAAM,KAAK,IAAI;wBAAE;oBAAQ;gBACjC;gBAEA,OAAO;oBAAE,OAAO;gBAAK;YACvB,EAAE,OAAO,OAAO;gBACd,OAAO;oBAAE;gBAAM;YACjB;QACF;QAEA,SAAS;YACP,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAC3B,IAAI;gBAAE,MAAM;gBAAM,SAAS;YAAK;QAClC;QAEA,eAAe,OAAO;YACpB,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,GAAG;gBACjB,IAAI,CAAC,MAAM,OAAO;oBAAE,OAAO;gBAAoB;gBAE/C,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,YACL,MAAM,CAAC;oBAAE,GAAG,OAAO;oBAAE,YAAY,IAAI,OAAO,WAAW;gBAAG,GAC1D,EAAE,CAAC,MAAM,KAAK,EAAE,EAChB,MAAM,GACN,MAAM;gBAET,IAAI,OAAO,OAAO;oBAAE;gBAAM;gBAE1B,IAAI;oBAAE,SAAS;gBAAK;gBACpB,OAAO;oBAAE,OAAO;gBAAK;YACvB,EAAE,OAAO,OAAO;gBACd,OAAO;oBAAE;gBAAM;YACjB;QACF;QAEA,YAAY;YACV,IAAI;gBACF,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;gBAE5D,IAAI,SAAS,MAAM;oBACjB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACrC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QAAQ,IAAI,CAAC,EAAE,EACxB,MAAM;oBAET,IAAI;wBAAE,MAAM,QAAQ,IAAI;wBAAE;oBAAQ;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;YAC5C,SAAU;gBACR,IAAI;oBAAE,SAAS;gBAAM;YACvB;YAEA,0BAA0B;YAC1B,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,OAAO;gBAC5C,IAAI,SAAS,MAAM;oBACjB,MAAM,EAAE,MAAM,OAAO,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACrC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,QAAQ,IAAI,CAAC,EAAE,EACxB,MAAM;oBAET,IAAI;wBAAE,MAAM,QAAQ,IAAI;wBAAE;oBAAQ;gBACpC,OAAO;oBACL,IAAI;wBAAE,MAAM;wBAAM,SAAS;oBAAK;gBAClC;YACF;QACF;IACF,CAAC"}}, {"offset": {"line": 215, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/GitHub/fbaug/facebook-clone/src/components/providers/auth-provider.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useAuthStore } from '@/lib/stores/auth-store'\n\ninterface AuthProviderProps {\n  children: React.ReactNode\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const initialize = useAuthStore((state) => state.initialize)\n\n  useEffect(() => {\n    initialize()\n  }, [initialize])\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AASO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,MAAM,aAAa,CAAA,GAAA,qIAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAU,MAAM,UAAU;IAE3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAW;IAEf,qBAAO;kBAAG;;AACZ"}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}