# Supabase Configuration
# Replace these with your actual Supabase project credentials
# Get them from: Supabase Dashboard → Settings → API
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_actual_anon_key_here

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Instructions:
# 1. Go to your Supabase dashboard
# 2. Navigate to Settings → API
# 3. Copy your Project URL and replace the URL above
# 4. Copy your anon/public key and replace the key above
# 5. Save this file and restart your development server
