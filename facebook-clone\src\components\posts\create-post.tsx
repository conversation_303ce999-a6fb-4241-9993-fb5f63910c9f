'use client'

import { useState } from 'react'
import { Image, Smile, MapPin } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { useAuthStore } from '@/lib/stores/auth-store'
import { usePostsStore } from '@/lib/stores/posts-store'

export function CreatePost() {
  const { user, profile } = useAuthStore()
  const { createPost } = usePostsStore()
  const [content, setContent] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!content.trim()) return

    setIsLoading(true)
    try {
      const { error } = await createPost(content.trim())
      if (!error) {
        setContent('')
      }
    } catch (err) {
      console.error('Error creating post:', err)
    } finally {
      setIsLoading(false)
    }
  }

  if (!user) return null

  return (
    <Card className="mb-6">
      <CardContent className="p-4">
        <form onSubmit={handleSubmit}>
          <div className="flex space-x-3">
            <Avatar className="w-10 h-10">
              <AvatarImage src={profile?.avatar_url || ''} />
              <AvatarFallback>
                {profile?.full_name?.charAt(0) || user.email?.charAt(0) || 'U'}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <Textarea
                placeholder={`What's on your mind, ${profile?.full_name?.split(' ')[0] || 'there'}?`}
                value={content}
                onChange={(e) => setContent(e.target.value)}
                className="min-h-[100px] resize-none border-none bg-gray-50 focus:bg-white"
              />
            </div>
          </div>

          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex space-x-4">
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="text-gray-600 hover:bg-gray-100"
                >
                  <Image className="w-5 h-5 mr-2 text-green-600" />
                  Photo/Video
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="text-gray-600 hover:bg-gray-100"
                >
                  <Smile className="w-5 h-5 mr-2 text-yellow-600" />
                  Feeling/Activity
                </Button>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="text-gray-600 hover:bg-gray-100"
                >
                  <MapPin className="w-5 h-5 mr-2 text-red-600" />
                  Check In
                </Button>
              </div>
              <Button
                type="submit"
                disabled={!content.trim() || isLoading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isLoading ? 'Posting...' : 'Post'}
              </Button>
            </div>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
